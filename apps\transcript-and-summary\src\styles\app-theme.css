/**
 * Transcript and Summary App Theme
 * 
 * App-specific theme customizations that work with both CRM and MFE base themes
 */

/* ==========================================================================
   App-Specific Theme Variables
   ========================================================================== */

/* CRM Theme Customizations - Colors now centralized in dynamics-crm-theme.css */

/* MFE Theme Customizations - Colors now centralized in zb-champion-mfe-theme.css */

/* ==========================================================================
   App Layout Components
   ========================================================================== */

/* App Container */
.transcript-app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  font-family: var(--theme-font-family);
}

/* App Header */
.transcript-header {
  height: var(--transcript-header-height);
  background: var(--theme-bg-primary);
  border-bottom: 1px solid var(--theme-border-primary);
  padding: 0 var(--transcript-content-padding);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--transcript-shadow);
}

.transcript-header__title {
  font-size: var(--transcript-title-size);
  font-weight: 600;
  color: var(--theme-text-primary);
  margin: 0;
}

.transcript-header__actions {
  display: flex;
  gap: var(--theme-spacing-sm);
  align-items: center;
}

/* Main Content Area */
.transcript-main {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.transcript-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: var(--transcript-content-padding);
  gap: var(--transcript-panel-gap);
  overflow-y: auto;
}

/* ==========================================================================
   Transcript-Specific Components
   ========================================================================== */

/* Transcript Panel */
.transcript-panel {
  background: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-primary);
  border-radius: var(--transcript-card-radius);
  box-shadow: var(--transcript-shadow);
  overflow: hidden;
  transition: box-shadow 0.2s ease;
}

.transcript-panel:hover {
  box-shadow: var(--transcript-shadow-hover);
}

.transcript-panel__header {
  background: var(--theme-bg-secondary);
  padding: var(--theme-spacing-md);
  border-bottom: 1px solid var(--theme-border-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.transcript-panel__title {
  font-size: var(--transcript-body-size);
  font-weight: 600;
  color: var(--theme-text-primary);
  margin: 0;
}

.transcript-panel__content {
  padding: var(--theme-spacing-lg);
}

/* Transcript Text Display */
.transcript-text {
  background: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-secondary);
  border-radius: var(--transcript-card-radius);
  padding: var(--theme-spacing-lg);
  font-size: var(--transcript-body-size);
  line-height: var(--transcript-line-height);
  color: var(--theme-text-primary);
  max-height: 400px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.transcript-text--empty {
  color: var(--theme-text-secondary);
  font-style: italic;
  text-align: center;
  padding: var(--theme-spacing-xl);
}

/* Summary Display */
.transcript-summary {
  background: linear-gradient(135deg, var(--transcript-primary), var(--transcript-secondary));
  color: white;
  border-radius: var(--transcript-card-radius);
  padding: var(--theme-spacing-lg);
  margin-top: var(--transcript-panel-gap);
}

.transcript-summary__title {
  font-size: var(--transcript-title-size);
  font-weight: 600;
  margin: 0 0 var(--theme-spacing-md) 0;
}

.transcript-summary__content {
  font-size: var(--transcript-body-size);
  line-height: var(--transcript-line-height);
  opacity: 0.95;
}

/* Action Buttons */
.transcript-button {
  height: var(--transcript-button-height);
  padding: 0 var(--theme-spacing-lg);
  border: none;
  border-radius: var(--theme-border-radius);
  font-size: var(--transcript-body-size);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--theme-spacing-xs);
}

.transcript-button--primary {
  background: var(--transcript-primary);
  color: white;
}

.transcript-button--primary:hover {
  background: var(--transcript-secondary);
  transform: translateY(-1px);
  box-shadow: var(--transcript-shadow-hover);
}

.transcript-button--secondary {
  background: var(--theme-bg-secondary);
  color: var(--theme-text-primary);
  border: 1px solid var(--theme-border-primary);
}

.transcript-button--secondary:hover {
  background: var(--theme-bg-hover);
  border-color: var(--transcript-primary);
}

/* Status Indicators */
.transcript-status {
  display: inline-flex;
  align-items: center;
  padding: var(--theme-spacing-xs) var(--theme-spacing-sm);
  border-radius: var(--theme-border-radius);
  font-size: var(--transcript-caption-size);
  font-weight: 500;
  gap: var(--theme-spacing-xs);
}

.transcript-status--processing {
  background: var(--transcript-info);
  color: white;
}

.transcript-status--complete {
  background: var(--transcript-success);
  color: white;
}

.transcript-status--error {
  background: var(--transcript-error);
  color: white;
}

/* Loading Spinner */
.transcript-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--theme-border-secondary);
  border-top: 2px solid var(--transcript-primary);
  border-radius: 50%;
  animation: transcript-spin 1s linear infinite;
}

@keyframes transcript-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

/* Mobile breakpoint */
@media (max-width: var(--theme-mobile-breakpoint)) {
  .transcript-header {
    padding: 0 var(--theme-spacing-sm);
  }
  
  .transcript-header__title {
    font-size: var(--transcript-body-size);
  }
  
  .transcript-content {
    padding: var(--theme-spacing-sm);
    gap: var(--theme-spacing-sm);
  }
  
  .transcript-panel__content {
    padding: var(--theme-spacing-md);
  }
  
  .transcript-text {
    max-height: 300px;
    padding: var(--theme-spacing-md);
  }
  
  .transcript-button {
    width: 100%;
    margin-bottom: var(--theme-spacing-xs);
  }
}

/* Tablet breakpoint */
@media (max-width: var(--theme-tablet-breakpoint)) {
  .transcript-main {
    flex-direction: column;
  }
  
  .transcript-content {
    padding: var(--theme-spacing-md);
  }
  
  .transcript-text {
    max-height: 350px;
  }
}

/* ==========================================================================
   Accessibility Enhancements
   ========================================================================== */

/* Focus states */
.transcript-button:focus,
.transcript-text:focus {
  outline: 2px solid var(--transcript-primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .transcript-panel {
    border-width: 2px;
  }
  
  .transcript-button {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .transcript-button,
  .transcript-panel {
    transition: none;
  }
  
  .transcript-spinner {
    animation: none;
  }
}
