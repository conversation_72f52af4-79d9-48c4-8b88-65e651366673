/* Simple Pagination */
.simple-pagination {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--theme-spacing-md, 16px) var(--theme-spacing-lg, 24px);
  background: var(--theme-bg-primary, #ffffff);
  border-top: 1px solid var(--grid-border-color, var(--theme-border-color, #e1e5e9));
  font-family: var(--theme-font-family, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
  gap: var(--theme-spacing-md, 16px);
  flex-wrap: wrap;
}

.simple-pagination__info {
  color: var(--theme-text-secondary, #605e5c);
  font-size: var(--theme-font-size-sm, 14px);
  font-weight: 500;
  white-space: nowrap;
}

.simple-pagination__controls {
  display: flex;
  align-items: center;
  gap: var(--theme-spacing-sm, 8px);
  flex: 1;
  justify-content: center;
}

.simple-pagination__numbers {
  display: flex;
  align-items: center;
  gap: var(--theme-spacing-xs, 4px);
}

.simple-pagination__btn {
  min-width: 36px;
  height: 32px;
  padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
  font-size: var(--theme-font-size-sm, 12px);
  font-weight: 500;
  border: 1px solid var(--grid-border-color, var(--theme-border-color, #e1e5e9));
  border-radius: var(--theme-border-radius-sm, 4px);
  background: var(--theme-bg-primary, #ffffff);
  color: var(--theme-text-primary, #323130);
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
}

.simple-pagination__btn:hover:not(:disabled) {
  background-color: var(--theme-hover-background, #f3f2f1);
  border-color: var(--grid-primary-color, var(--theme-primary-color, #0078d4));
  color: var(--grid-primary-color, var(--theme-primary-color, #0078d4));
}

.simple-pagination__btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--theme-bg-disabled, #f3f2f1);
  border-color: var(--theme-border-disabled, #d2d0ce);
  color: var(--theme-text-disabled, #a19f9d);
}

.simple-pagination__btn--number {
  min-width: 32px;
  padding: var(--theme-spacing-xs, 4px);
}

.simple-pagination__btn--active {
  background-color: var(--grid-primary-color, var(--theme-primary-color, #0078d4));
  border-color: var(--grid-primary-color, var(--theme-primary-color, #0078d4));
  color: var(--theme-button-text, #ffffff);
}

.simple-pagination__btn--active:hover {
  background-color: var(--theme-primary-hover, #106ebe);
  border-color: var(--theme-primary-hover, #106ebe);
  color: var(--theme-button-text, #ffffff);
}

.simple-pagination__btn--prev,
.simple-pagination__btn--next {
  min-width: 80px;
}

.simple-pagination__ellipsis {
  padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
  color: var(--theme-text-muted, #8a8886);
  font-size: var(--theme-font-size-sm, 12px);
  user-select: none;
}

/* Page Size Selector */
.simple-pagination__page-size {
  display: flex;
  align-items: center;
  gap: var(--theme-spacing-sm, 8px);
  white-space: nowrap;
}

.simple-pagination__page-size-label {
  color: var(--theme-text-secondary, #605e5c);
  font-size: var(--theme-font-size-sm, 14px);
  font-weight: 500;
}

.simple-pagination__page-size-select {
  padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
  border: 1px solid var(--grid-border-color, var(--theme-border-color, #e1e5e9));
  border-radius: var(--theme-border-radius-sm, 2px);
  font-size: var(--theme-font-size-sm, 12px);
  background: var(--theme-input-background, #ffffff);
  color: var(--theme-text-color, #323130);
  outline: none;
  cursor: pointer;
  min-width: 60px;
}

.simple-pagination__page-size-select:focus {
  border-color: var(--grid-primary-color, var(--theme-primary-color, #0078d4));
  box-shadow: 0 0 0 1px var(--grid-primary-color, var(--theme-primary-color, #0078d4));
}

/* Responsive Design */
@media (max-width: 768px) {
  .simple-pagination {
    flex-direction: column;
    align-items: stretch;
    gap: var(--theme-spacing-sm, 8px);
    padding: var(--theme-spacing-sm, 8px) var(--theme-spacing-md, 16px);
  }
  
  .simple-pagination__info {
    text-align: center;
    font-size: var(--theme-font-size-xs, 12px);
  }
  
  .simple-pagination__controls {
    justify-content: center;
    width: 100%;
  }
  
  .simple-pagination__numbers {
    gap: var(--theme-spacing-xs, 2px);
  }
  
  .simple-pagination__btn {
    min-width: 28px;
    height: 28px;
    font-size: var(--theme-font-size-xs, 11px);
  }
  
  .simple-pagination__btn--prev,
  .simple-pagination__btn--next {
    min-width: 60px;
  }
  
  .simple-pagination__page-size {
    justify-content: center;
  }
  
  .simple-pagination__page-size-label,
  .simple-pagination__page-size-select {
    font-size: var(--theme-font-size-xs, 12px);
  }
}

@media (max-width: 480px) {
  .simple-pagination__numbers {
    display: none;
  }
  
  .simple-pagination__controls {
    justify-content: space-between;
  }
  
  .simple-pagination__info {
    font-size: var(--theme-font-size-xs, 11px);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .simple-pagination {
    border-top-width: 2px;
  }
  
  .simple-pagination__btn:focus {
    outline: 2px solid var(--grid-primary-color, var(--theme-primary-color, #0078d4));
    outline-offset: 2px;
  }
  
  .simple-pagination__page-size-select:focus {
    box-shadow: 0 0 0 2px var(--grid-primary-color, var(--theme-primary-color, #0078d4));
  }
}

/* Dark Mode */
@media (prefers-color-scheme: dark) {
  .simple-pagination {
    background: var(--theme-bg-primary, #2d2d2d);
    border-top-color: var(--theme-border-color, #404040);
  }
  
  .simple-pagination__btn {
    background: var(--theme-bg-primary, #2d2d2d);
    border-color: var(--theme-border-color, #404040);
    color: var(--theme-text-primary, #ffffff);
  }
  
  .simple-pagination__btn:hover:not(:disabled) {
    background-color: var(--theme-hover-background, #333333);
  }
  
  .simple-pagination__page-size-select {
    background: var(--theme-input-background, #2d2d2d);
    border-color: var(--theme-border-color, #404040);
    color: var(--theme-text-color, #ffffff);
  }
}
