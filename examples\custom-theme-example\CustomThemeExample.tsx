/**
 * Custom Theme Example
 *
 * Comprehensive demonstration of the theme switching system including:
 * - ThemeProvider usage with CRM and MFE themes
 * - Manual theme switching with buttons and controls
 * - Theme-aware hooks (useTheme, useThemeStyles, useCurrentTheme)
 * - Components that adapt to different themes using CSS custom properties
 * - Responsive design across different themes
 * - Theme persistence and auto-detection
 */

import React, { useState, useEffect } from 'react';

// Note: In a real implementation, these would be imported from the actual shared modules
// For this example, we'll create mock implementations to demonstrate the concepts

// Mock ThemeMode enum
enum ThemeMode {
  CRM = 'crm',
  MFE = 'mfe'
}

// Mock theme context and hooks
const ThemeContext = React.createContext<any>(null);

const useTheme = () => {
  const [currentTheme, setCurrentTheme] = useState(ThemeMode.CRM);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const switchTheme = async (theme: ThemeMode) => {
    setIsLoading(true);
    setError(null);
    try {
      // Simulate theme switching
      await new Promise(resolve => setTimeout(resolve, 300));
      setCurrentTheme(theme);
      document.documentElement.setAttribute('data-theme', theme);
      localStorage.setItem('theme-preference', theme);
    } catch (err) {
      setError('Failed to switch theme');
    } finally {
      setIsLoading(false);
    }
  };

  return { currentTheme, switchTheme, isLoading, error };
};

const useCurrentTheme = () => {
  const { currentTheme } = useTheme();
  return currentTheme;
};

const useIsTheme = (theme: ThemeMode) => {
  const currentTheme = useCurrentTheme();
  return currentTheme === theme;
};

const useThemeStyles = () => {
  const getCSSVariable = (varName: string) => {
    if (typeof window !== 'undefined') {
      return getComputedStyle(document.documentElement).getPropertyValue(varName);
    }
    return '';
  };

  const getThemeClass = (baseClass: string) => {
    const currentTheme = useCurrentTheme();
    return `${baseClass} theme-${currentTheme}`;
  };

  return { getCSSVariable, getThemeClass };
};

// Mock Button component
const Button: React.FC<{
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'small' | 'medium' | 'large';
  onClick?: () => void;
  disabled?: boolean;
  children: React.ReactNode;
}> = ({ variant = 'primary', size = 'medium', onClick, disabled, children }) => {
  return (
    <button
      className={`theme-button theme-button--${variant} theme-button--${size}`}
      onClick={onClick}
      disabled={disabled}
      style={{
        padding: 'var(--theme-spacing-md)',
        borderRadius: 'var(--theme-border-radius)',
        border: 'none',
        cursor: disabled ? 'not-allowed' : 'pointer',
        opacity: disabled ? 0.6 : 1,
        transition: 'all 0.2s ease'
      }}
    >
      {children}
    </button>
  );
};

// Mock ThemeSwitcher component
const ThemeSwitcherComponent: React.FC = () => {
  const { currentTheme, switchTheme, isLoading } = useTheme();

  return (
    <select
      value={currentTheme}
      onChange={(e) => switchTheme(e.target.value as ThemeMode)}
      disabled={isLoading}
      style={{
        padding: 'var(--theme-spacing-sm)',
        borderRadius: 'var(--theme-border-radius)',
        border: '1px solid var(--theme-border-primary)',
        backgroundColor: 'var(--theme-bg-primary)',
        color: 'var(--theme-text-primary)',
        fontSize: 'var(--theme-font-size-md)'
      }}
    >
      <option value={ThemeMode.CRM}>🏢 Dynamics CRM Theme</option>
      <option value={ThemeMode.MFE}>🎯 ZB Champion Theme</option>
    </select>
  );
};

// Mock ThemeProvider
const ThemeProvider: React.FC<{
  children: React.ReactNode;
  defaultTheme?: ThemeMode;
  enableAutoDetection?: boolean;
  enablePersistence?: boolean;
  storageKey?: string;
}> = ({ children, defaultTheme = ThemeMode.CRM, enablePersistence = false, storageKey = 'theme' }) => {
  useEffect(() => {
    // Initialize theme
    let initialTheme = defaultTheme;

    if (enablePersistence && typeof window !== 'undefined') {
      const saved = localStorage.getItem(storageKey);
      if (saved && Object.values(ThemeMode).includes(saved as ThemeMode)) {
        initialTheme = saved as ThemeMode;
      }
    }

    document.documentElement.setAttribute('data-theme', initialTheme);

    // Add CSS custom properties for demonstration
    const root = document.documentElement;
    if (initialTheme === ThemeMode.CRM) {
      root.style.setProperty('--theme-primary', '#0078d4');
      root.style.setProperty('--theme-secondary', '#106ebe');
      root.style.setProperty('--theme-bg-primary', '#ffffff');
      root.style.setProperty('--theme-bg-secondary', '#f3f2f1');
      root.style.setProperty('--theme-text-primary', '#323130');
      root.style.setProperty('--theme-text-secondary', '#605e5c');
      root.style.setProperty('--theme-border-primary', '#d2d0ce');
      root.style.setProperty('--theme-border-radius', '2px');
      root.style.setProperty('--theme-font-family', '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif');
      root.style.setProperty('--theme-spacing-sm', '8px');
      root.style.setProperty('--theme-spacing-md', '16px');
      root.style.setProperty('--theme-spacing-lg', '24px');
      root.style.setProperty('--theme-font-size-sm', '12px');
      root.style.setProperty('--theme-font-size-md', '14px');
      root.style.setProperty('--theme-font-size-lg', '16px');
      root.style.setProperty('--theme-font-size-3xl', '32px');
    } else {
      root.style.setProperty('--theme-primary', '#5e10b1');
      root.style.setProperty('--theme-secondary', '#7b2cbf');
      root.style.setProperty('--theme-bg-primary', '#ffffff');
      root.style.setProperty('--theme-bg-secondary', '#f8f9fa');
      root.style.setProperty('--theme-text-primary', '#212529');
      root.style.setProperty('--theme-text-secondary', '#6c757d');
      root.style.setProperty('--theme-border-primary', '#dee2e6');
      root.style.setProperty('--theme-border-radius', '16px');
      root.style.setProperty('--theme-font-family', '"RNHouseSans", Arial, sans-serif');
      root.style.setProperty('--theme-spacing-sm', '12px');
      root.style.setProperty('--theme-spacing-md', '24px');
      root.style.setProperty('--theme-spacing-lg', '32px');
      root.style.setProperty('--theme-font-size-sm', '14px');
      root.style.setProperty('--theme-font-size-md', '16px');
      root.style.setProperty('--theme-font-size-lg', '18px');
      root.style.setProperty('--theme-font-size-3xl', '36px');
    }
  }, [defaultTheme, enablePersistence, storageKey]);

  return <div>{children}</div>;
};

// Example component showing theme differences
const ThemeShowcase: React.FC = () => {
  const { currentTheme } = useTheme();
  const { getThemeClass, getCSSVariable } = useThemeStyles();
  
  const primaryColor = getCSSVariable('--theme-primary');
  const fontFamily = getCSSVariable('--theme-font-family');
  
  return (
    <div className={getThemeClass('theme-card')} style={{ marginBottom: '24px' }}>
      <div className="theme-card-header">
        <h3 className="theme-card-title">Current Theme: {currentTheme}</h3>
      </div>
      <div className="theme-card-content">
        <div style={{ marginBottom: '16px' }}>
          <p className="theme-text-primary">
            <strong>Primary Color:</strong> {primaryColor}
          </p>
          <p className="theme-text-primary">
            <strong>Font Family:</strong> {fontFamily}
          </p>
        </div>
        
        <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
          <Button variant="primary" size="medium">
            Primary Button
          </Button>
          <Button variant="secondary" size="medium">
            Secondary Button
          </Button>
          <Button variant="danger" size="medium">
            Danger Button
          </Button>
        </div>
        
        <div style={{ marginTop: '16px' }}>
          <input 
            className="theme-input" 
            placeholder="Theme-aware input field"
            style={{ width: '100%', marginBottom: '8px' }}
          />
          <select className="theme-input theme-select" style={{ width: '100%' }}>
            <option>Theme-aware select</option>
            <option>Option 1</option>
            <option>Option 2</option>
          </select>
        </div>
      </div>
    </div>
  );
};

// Component showing theme-specific features
const ThemeFeatures: React.FC = () => {
  const currentTheme = useCurrentTheme();
  
  return (
    <div className="theme-card" style={{ marginBottom: '24px' }}>
      <div className="theme-card-header">
        <h3 className="theme-card-title">Theme-Specific Features</h3>
      </div>
      <div className="theme-card-content">
        {currentTheme === ThemeMode.CRM && (
          <div>
            <h4 className="theme-text-primary">🏢 Dynamics CRM Theme</h4>
            <ul className="theme-text-secondary">
              <li>Segoe UI font family for enterprise consistency</li>
              <li>Conservative 4px border radius</li>
              <li>Dynamics 365 blue (#0078d4) primary color</li>
              <li>Subtle shadows and spacing</li>
              <li>32px button height for accessibility</li>
              <li>Enterprise-grade form controls</li>
            </ul>
            <div style={{ 
              padding: '12px', 
              backgroundColor: 'var(--theme-primary-light)',
              borderLeft: '4px solid var(--theme-primary)',
              marginTop: '12px'
            }}>
              <p className="theme-text-primary" style={{ margin: 0 }}>
                This theme is optimized for Dynamics 365 integration and enterprise environments.
              </p>
            </div>
          </div>
        )}
        
        {currentTheme === ThemeMode.MFE && (
          <div>
            <h4 className="theme-text-primary">🎯 ZB Champion Theme</h4>
            <ul className="theme-text-secondary">
              <li>RNHouseSans font family for brand consistency</li>
              <li>Modern 16px border radius</li>
              <li>ZB Champion purple (#5e10b1) primary color</li>
              <li>Enhanced visual effects and gradients</li>
              <li>44px button height for touch-friendly interface</li>
              <li>Mobile-responsive design (840px breakpoint)</li>
            </ul>
            <div style={{ 
              padding: '12px', 
              backgroundColor: 'var(--theme-primary-light)',
              borderLeft: '4px solid var(--theme-primary)',
              marginTop: '12px',
              borderRadius: '8px'
            }}>
              <p className="theme-text-primary" style={{ margin: 0 }}>
                This theme provides modern, branded styling for standalone applications.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Component showing CSS variables
const CSSVariablesDemo: React.FC = () => {
  const { getCSSVariable } = useThemeStyles();
  
  const variables = [
    { name: 'Primary Color', var: '--theme-primary' },
    { name: 'Secondary Color', var: '--theme-secondary' },
    { name: 'Background', var: '--theme-bg-primary' },
    { name: 'Text Color', var: '--theme-text-primary' },
    { name: 'Border Color', var: '--theme-border-primary' },
    { name: 'Font Family', var: '--theme-font-family' },
    { name: 'Border Radius', var: '--theme-radius-base' },
    { name: 'Shadow', var: '--theme-shadow-base' },
  ];
  
  return (
    <div className="theme-card">
      <div className="theme-card-header">
        <h3 className="theme-card-title">CSS Custom Properties</h3>
      </div>
      <div className="theme-card-content">
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', 
          gap: '12px' 
        }}>
          {variables.map(({ name, var: varName }) => (
            <div 
              key={varName}
              style={{
                padding: '12px',
                backgroundColor: 'var(--theme-bg-tertiary)',
                border: '1px solid var(--theme-border-primary)',
                borderRadius: 'var(--theme-radius-base)',
                fontSize: 'var(--theme-font-size-sm)'
              }}
            >
              <div style={{ 
                fontWeight: 'var(--theme-font-weight-medium)',
                color: 'var(--theme-text-primary)',
                marginBottom: '4px'
              }}>
                {name}
              </div>
              <div style={{ 
                color: 'var(--theme-text-secondary)',
                fontFamily: 'monospace',
                fontSize: '0.85em'
              }}>
                {getCSSVariable(varName) || 'Not set'}
              </div>
              <div style={{ 
                color: 'var(--theme-text-tertiary)',
                fontSize: '0.8em',
                marginTop: '2px'
              }}>
                {varName}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Main example content
const CustomThemeExampleContent: React.FC = () => {
  const { switchTheme, isLoading, error } = useTheme();
  const currentTheme = useCurrentTheme();
  
  const handleQuickSwitch = async () => {
    const newTheme = currentTheme === ThemeMode.CRM ? ThemeMode.MFE : ThemeMode.CRM;
    await switchTheme(newTheme);
  };
  
  return (
    <div style={{ 
      padding: '24px', 
      maxWidth: '1200px', 
      margin: '0 auto',
      fontFamily: 'var(--theme-font-family)',
      minHeight: '100vh',
      backgroundColor: 'var(--theme-bg-secondary)'
    }}>
      <header style={{ 
        marginBottom: '32px',
        textAlign: 'center'
      }}>
        <h1 className="theme-text-primary" style={{ 
          fontSize: 'var(--theme-font-size-3xl)',
          marginBottom: '16px'
        }}>
          Custom Theme System Demo
        </h1>
        <p className="theme-text-secondary" style={{ 
          fontSize: 'var(--theme-font-size-lg)',
          marginBottom: '24px'
        }}>
          Demonstrating Dynamics CRM and ZB Champion themes
        </p>
        
        <div style={{ 
          display: 'flex', 
          gap: '16px', 
          justifyContent: 'center',
          alignItems: 'center',
          flexWrap: 'wrap'
        }}>
          <Button 
            variant="primary" 
            onClick={handleQuickSwitch}
            disabled={isLoading}
          >
            {isLoading ? 'Switching...' : `Switch to ${currentTheme === ThemeMode.CRM ? 'ZB Champion' : 'Dynamics CRM'}`}
          </Button>
          
          <ThemeSwitcherComponent />
        </div>
        
        {error && (
          <div style={{
            marginTop: '16px',
            padding: '12px',
            backgroundColor: 'var(--theme-error-light)',
            color: 'var(--theme-error)',
            border: '1px solid var(--theme-border-error)',
            borderRadius: 'var(--theme-radius-base)'
          }}>
            <strong>Error:</strong> {error}
          </div>
        )}
      </header>
      
      <div style={{ 
        display: 'grid', 
        gap: '24px'
      }}>
        <ThemeShowcase />
        <ThemeFeatures />
        <CSSVariablesDemo />
      </div>
      
      <footer style={{ 
        marginTop: '48px',
        textAlign: 'center',
        padding: '24px',
        borderTop: '1px solid var(--theme-border-tertiary)'
      }}>
        <p className="theme-text-secondary">
          Theme system supports runtime switching between deployment contexts
        </p>
      </footer>
    </div>
  );
};

// Main example component with ThemeProvider
export const CustomThemeExample: React.FC = () => {
  return (
    <ThemeProvider 
      enableAutoDetection={true}
      enablePersistence={true}
      storageKey="custom-theme-example"
    >
      <CustomThemeExampleContent />
    </ThemeProvider>
  );
};

export default CustomThemeExample;
