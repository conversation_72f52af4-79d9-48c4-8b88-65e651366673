{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "noEmit": true, "baseUrl": ".", "paths": {"@shared/*": ["../../shared/*"], "@shared/components": ["../../shared/components"], "@shared/components/*": ["../../shared/components/*"], "@shared/services": ["../../shared/services"], "@shared/services/*": ["../../shared/services/*"], "@shared/utils": ["../../shared/utils"], "@shared/utils/*": ["../../shared/utils/*"]}}, "include": ["src/**/*"], "references": [{"path": "../../shared"}], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx", "src/__tests__/**/*"]}