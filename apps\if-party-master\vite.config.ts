import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig(({ mode }) => {
  const deploymentMode = process.env.VITE_DEPLOYMENT_MODE || 'web_resource';
  const isWebResource = deploymentMode === 'web_resource';

  return {
    plugins: [react()],
    css: {
      preprocessorOptions: {
        css: {
          charset: false,
        },
      },
    },
    resolve: {
      alias: {
        '@shared': resolve(__dirname, '../../shared'),
        '@shared/components': resolve(__dirname, '../../shared/components'),
        '@shared/services': resolve(__dirname, '../../shared/services'),
        '@shared/utils': resolve(__dirname, '../../shared/utils'),
        '@shared/config': resolve(__dirname, '../../shared/config'),
        '@shared/styles': resolve(__dirname, '../../shared/styles'),
      },
    },
    define: {
      'import.meta.env.VITE_DEPLOYMENT_MODE': JSON.stringify(deploymentMode),
      'import.meta.env.VITE_THEME_MODE': JSON.stringify(process.env.VITE_THEME_MODE),
    },
    server: {
      port: 5174,
      open: true,
    },
    build: {
      outDir: isWebResource ? 'dist/webresource' : 'dist',
      sourcemap: !isWebResource, // No sourcemaps for web resources
      minify: isWebResource ? 'terser' : true,
      rollupOptions: {
        output: isWebResource ? {
          // Web Resource mode: Single file output
          entryFileNames: 'if-party-master.js',
          chunkFileNames: 'if-party-master.js',
          assetFileNames: (assetInfo) => {
            if (assetInfo.name?.endsWith('.css')) {
              return 'if-party-master.css';
            }
            return '[name].[ext]';
          },
          manualChunks: undefined, // Disable code splitting
          inlineDynamicImports: true, // Inline all dynamic imports
        } : {
          // Standalone mode: Optimized chunking
          assetFileNames: (assetInfo) => {
            if (assetInfo.name?.endsWith('.css')) {
              if (assetInfo.name.includes('theme')) {
                return 'assets/themes/[name].[ext]';
              }
              return 'assets/styles/[name].[ext]';
            }
            return 'assets/[name].[ext]';
          },
        },
      },
      // Web resource specific optimizations
      ...(isWebResource && {
        cssCodeSplit: false, // Single CSS file
        assetsInlineLimit: 0, // Don't inline assets for web resources
        terserOptions: {
          compress: {
            drop_console: true, // Remove console logs in web resource builds
            drop_debugger: true,
          },
        },
      }),
    },
  };
});
