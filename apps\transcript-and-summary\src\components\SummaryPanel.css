/* Summary Panel Styles */
.summary-panel {
  width: 350px;
  padding: var(--theme-spacing-xl, 2rem);
  background-color: var(--theme-bg-secondary, #f9fafb);
  border-left: 1px solid var(--theme-border-color, #e5e7eb);
}

.summary-panel__title {
  margin: 0 0 var(--theme-spacing-lg, 1rem) 0;
  color: var(--theme-text-primary, #1f2937);
  font-size: var(--theme-font-size-lg, 18px);
  font-weight: 600;
}

/* Summary Section */
.summary-section {
  margin-bottom: var(--theme-spacing-xl, 2rem);
}

.summary-section__title {
  margin: 0 0 var(--theme-spacing-sm, 0.5rem) 0;
  font-size: var(--theme-font-size-sm, 0.875rem);
  font-weight: 600;
  color: var(--theme-text-secondary, #374151);
}

.summary-content {
  background-color: var(--theme-bg-primary, #fff);
  border: 1px solid var(--theme-border-color, #e5e7eb);
  border-radius: var(--theme-border-radius, 0.375rem);
  padding: var(--theme-spacing-lg, 1rem);
  min-height: 100px;
}

.summary-text {
  margin: 0;
  line-height: 1.5;
  color: var(--theme-text-primary, #374151);
  font-size: var(--theme-font-size-sm, 0.875rem);
}

.summary-placeholder {
  color: var(--theme-text-muted, #9ca3af);
  font-style: italic;
  margin: 0;
}

/* Insights Section */
.insights-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.insight-item {
  display: flex;
  align-items: flex-start;
  gap: var(--theme-spacing-sm, 0.5rem);
  margin-bottom: var(--theme-spacing-md, 1rem);
  padding: var(--theme-spacing-md, 1rem);
  background-color: var(--theme-bg-primary, #fff);
  border: 1px solid var(--theme-border-color, #e5e7eb);
  border-radius: var(--theme-border-radius, 0.375rem);
}

.insight-icon {
  width: 16px;
  height: 16px;
  margin-top: 2px;
  color: var(--theme-primary-color, #3b82f6);
}

.insight-text {
  flex: 1;
  font-size: var(--theme-font-size-sm, 0.875rem);
  line-height: 1.4;
  color: var(--theme-text-primary, #374151);
  margin: 0;
}

/* Actions Section */
.actions-section {
  margin-top: var(--theme-spacing-xl, 2rem);
}

.actions-grid {
  display: flex;
  flex-direction: column;
  gap: var(--theme-spacing-sm, 0.5rem);
}

.action-button {
  width: 100% !important;
  padding: var(--theme-spacing-sm, 0.5rem) var(--theme-spacing-md, 1rem);
  font-size: var(--theme-font-size-sm, 0.875rem);
  border-radius: var(--theme-border-radius, 0.375rem);
  border: 1px solid var(--theme-border-color, #e5e7eb);
  background-color: var(--theme-bg-primary, #fff);
  color: var(--theme-text-primary, #374151);
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  background-color: var(--theme-hover-background, #f9fafb);
  border-color: var(--theme-primary-color, #3b82f6);
}

/* Theme colors now centralized in main theme files */
