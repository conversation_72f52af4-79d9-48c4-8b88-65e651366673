import React, { useState, useEffect } from 'react';
import { Button } from '@shared/components';
import { useAuth } from '@shared/services';
import { ThemeProvider } from '@shared/services/theme';
import { logger } from '@shared/utils';
import CallLogPage from './components/CallLogPage';
import CallDetailPage from './components/CallDetailPage';
import { CallRecord } from './services/mockDataService';

// Navigation state type
type AppView = 'list' | 'detail';

function App() {
  const [currentView, setCurrentView] = useState<AppView>('list');
  const [selectedCallRecord, setSelectedCallRecord] = useState<CallRecord | null>(null);
  const { user, isAuthenticated, login } = useAuth();

  useEffect(() => {
    logger.info('Transcript and Summary app initialized');
  }, []);

  // Navigation handlers
  const handleViewCall = (callRecord: CallRecord) => {
    setSelectedCallRecord(callRecord);
    setCurrentView('detail');
    logger.info('Navigating to call detail', { callId: callRecord.id });
  };

  const handleBackToList = () => {
    setCurrentView('list');
    setSelectedCallRecord(null);
    logger.info('Navigating back to call list');
  };

  // if (!isAuthenticated) {
  //   return (
  //     <ThemeProvider enableAutoDetection={true} enablePersistence={true}>
  //       <div className="transcript-app" style={{
  //         display: 'flex',
  //         flexDirection: 'column',
  //         alignItems: 'center',
  //         justifyContent: 'center',
  //         minHeight: '100vh',
  //         padding: '20px'
  //       }}>
  //         <h1>Transcript and Summary</h1>
  //         <p style={{ marginBottom: '20px' }}>
  //           Please log in to access the application.
  //         </p>
  //         <Button
  //           onClick={() => login({ email: '<EMAIL>', password: 'password' })}
  //           variant="primary"
  //         >
  //           Demo Login
  //         </Button>
  //       </div>
  //     </ThemeProvider>
  //   );
  // }

  return (
    <ThemeProvider enableAutoDetection={true} enablePersistence={true}>
      <div className="transcript-app" data-testid="app-container">
        {currentView === 'list' ? (
          <CallLogPage onViewCall={handleViewCall} />
        ) : (
          selectedCallRecord && (
            <CallDetailPage
              callRecord={selectedCallRecord}
              onBack={handleBackToList}
            />
          )
        )}
      </div>
    </ThemeProvider>
  );
}

export default App;
