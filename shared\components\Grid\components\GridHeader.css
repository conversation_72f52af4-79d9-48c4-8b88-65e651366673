/* Grid Header */
.grid__header {
  background: var(--grid-header-bg, var(--theme-bg-secondary, #f8f9fa));
  border-bottom: 2px solid var(--grid-border-color, var(--theme-border-color, #e1e5e9));
  position: relative;
  z-index: 10;
}

.grid__header-row {
  display: flex;
  align-items: center;
  min-height: var(--grid-header-height, 48px);
  background: inherit;
}

/* Header Cell */
.grid__header-cell {
  display: flex;
  align-items: center;
  padding: var(--grid-header-padding, 8px 12px);
  background: inherit;
  border-right: 1px solid var(--grid-border-color, var(--theme-border-color, #e1e5e9));
  position: relative;
  user-select: none;
  font-weight: 600;
  color: var(--theme-text-primary, #323130);
  font-size: var(--theme-font-size-sm, 14px);
  min-height: inherit;
  box-sizing: border-box;
}

.grid__header-cell:last-child {
  border-right: none;
}

/* Header Content */
.grid__header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: var(--theme-spacing-xs, 4px);
}

.grid__header-title {
  flex: 1;
  font-weight: 600;
  color: var(--theme-text-primary, #323130);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Sortable Header */
.grid__header-cell--sortable .grid__header-title {
  cursor: pointer;
  transition: color 0.2s ease;
}

.grid__header-cell--sortable .grid__header-title:hover {
  color: var(--grid-primary-color, var(--theme-primary-color, #0078d4));
}

/* Sort Icon */
.grid__sort-icon {
  font-size: 12px;
  color: var(--grid-primary-color, var(--theme-primary-color, #0078d4));
  font-weight: bold;
  margin-left: var(--theme-spacing-xs, 4px);
}

/* Filter Toggle */
.grid__filter-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px 4px;
  border-radius: var(--theme-border-radius-sm, 2px);
  font-size: 12px;
  opacity: 0.7;
  transition: all 0.2s ease;
  color: var(--theme-text-secondary, #605e5c);
}

.grid__filter-toggle:hover {
  opacity: 1;
  background-color: var(--theme-hover-background, #f3f2f1);
  color: var(--grid-primary-color, var(--theme-primary-color, #0078d4));
}

/* Filter Dropdown */
.grid__header-filter {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--theme-bg-primary, #ffffff);
  border: 1px solid var(--grid-border-color, var(--theme-border-color, #e1e5e9));
  border-top: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  padding: var(--theme-spacing-sm, 8px);
}

.grid__filter-input {
  width: 100%;
  padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
  border: 1px solid var(--grid-border-color, var(--theme-border-color, #e1e5e9));
  border-radius: var(--theme-border-radius-sm, 2px);
  font-size: var(--theme-font-size-sm, 12px);
  background: var(--theme-input-background, #ffffff);
  color: var(--theme-text-color, #323130);
  outline: none;
  transition: border-color 0.2s ease;
}

.grid__filter-input:focus {
  border-color: var(--grid-primary-color, var(--theme-primary-color, #0078d4));
  box-shadow: 0 0 0 1px var(--grid-primary-color, var(--theme-primary-color, #0078d4));
}

/* Resize Handle */
.grid__resize-handle {
  position: absolute;
  top: 0;
  right: -2px;
  bottom: 0;
  width: 4px;
  cursor: col-resize;
  background: transparent;
  transition: background-color 0.2s ease;
}

.grid__resize-handle:hover,
.grid__header-cell--resizing .grid__resize-handle {
  background-color: var(--grid-primary-color, var(--theme-primary-color, #0078d4));
}

/* Selection Column */
.grid__header-cell--selection {
  width: 40px;
  min-width: 40px;
  max-width: 40px;
  justify-content: center;
  padding: var(--theme-spacing-xs, 4px);
}

.grid__header-cell--selection input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
}

/* Pinned Columns */
.grid__header-cell--pinned-left {
  position: sticky;
  left: 0;
  z-index: 15;
  background: var(--grid-header-bg, var(--theme-bg-secondary, #f8f9fa));
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

.grid__header-cell--pinned-right {
  position: sticky;
  right: 0;
  z-index: 15;
  background: var(--grid-header-bg, var(--theme-bg-secondary, #f8f9fa));
  box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .grid__header-cell {
    padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 6px);
    font-size: var(--theme-font-size-xs, 12px);
    min-height: 40px;
  }
  
  .grid__header-title {
    font-size: var(--theme-font-size-xs, 12px);
  }
  
  .grid__filter-toggle {
    font-size: 10px;
    padding: 1px 2px;
  }
  
  .grid__sort-icon {
    font-size: 10px;
  }
  
  .grid__resize-handle {
    width: 6px;
    right: -3px;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .grid__header {
    border-bottom-width: 3px;
  }
  
  .grid__header-cell {
    border-right-width: 2px;
  }
  
  .grid__header-title:hover {
    background-color: var(--theme-text-primary, #323130);
    color: var(--theme-bg-primary, #ffffff);
  }
}

/* Focus Styles */
.grid__header-cell:focus-within {
  outline: 2px solid var(--grid-primary-color, var(--theme-primary-color, #0078d4));
  outline-offset: -2px;
}

.grid__filter-toggle:focus {
  outline: 2px solid var(--grid-primary-color, var(--theme-primary-color, #0078d4));
  outline-offset: 1px;
}

/* Animation for filter dropdown */
.grid__header-filter {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .grid__header-filter {
    animation: none;
  }
  
  .grid__filter-toggle,
  .grid__header-title {
    transition: none;
  }
}
