# Dependencies
node_modules/
**/node_modules/

# Build outputs
dist/
build/
coverage/
**/dist/
**/build/
**/coverage/

# Generated files
*.d.ts
*.d.ts.map

# Package files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# PCF specific
pcf/**/dist/
pcf/**/*.d.ts
pcf/**/*.js.map

# Documentation that should preserve formatting
CHANGELOG.md
LICENSE
