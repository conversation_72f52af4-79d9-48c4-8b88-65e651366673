/* Grid Row */
.grid__row {
  display: flex;
  align-items: center;
  min-height: var(--grid-row-height, 56px);
  border-bottom: 1px solid var(--grid-border-color, var(--theme-border-color, #e1e5e9));
  background: var(--theme-bg-primary, #ffffff);
  transition: background-color 0.2s ease;
  cursor: pointer;
  position: relative;
}

.grid__row:hover {
  background-color: var(--grid-hover-bg, var(--theme-hover-background, #f5f5f5));
}

.grid__row:focus {
  outline: 2px solid var(--grid-primary-color, var(--theme-primary-color, #0078d4));
  outline-offset: -2px;
  z-index: 1;
}

/* Row States */
.grid__row--selected {
  background-color: var(--grid-selected-bg, var(--theme-selected-background, #deecf9)) !important;
}

.grid__row--selected:hover {
  background-color: var(--grid-selected-bg, var(--theme-selected-background, #deecf9)) !important;
}

.grid__row--even {
  /* Used for zebra striping if enabled */
}

.grid__row--odd {
  /* Used for zebra striping if enabled */
}

.grid__row--expanded {
  border-bottom-color: transparent;
}

.grid__row--group {
  background-color: var(--theme-bg-secondary, #f8f9fa);
  font-weight: 600;
  border-bottom: 2px solid var(--grid-border-color, var(--theme-border-color, #e1e5e9));
}

/* Selection Cell */
.grid__cell--selection {
  width: 40px;
  min-width: 40px;
  max-width: 40px;
  justify-content: center;
  padding: var(--theme-spacing-xs, 4px);
  border-right: 1px solid var(--grid-border-color, var(--theme-border-color, #e1e5e9));
}

.grid__cell--selection input[type="checkbox"],
.grid__cell--selection input[type="radio"] {
  margin: 0;
  cursor: pointer;
  transform: scale(1.1);
}

.grid__cell--selection input[type="checkbox"]:focus,
.grid__cell--selection input[type="radio"]:focus {
  outline: 2px solid var(--grid-primary-color, var(--theme-primary-color, #0078d4));
  outline-offset: 2px;
}

/* Expand Cell */
.grid__cell--expand {
  width: 32px;
  min-width: 32px;
  max-width: 32px;
  justify-content: center;
  padding: var(--theme-spacing-xs, 4px);
  border-right: 1px solid var(--grid-border-color, var(--theme-border-color, #e1e5e9));
}

.grid__expand-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px;
  border-radius: var(--theme-border-radius-sm, 2px);
  transition: all 0.2s ease;
  color: var(--theme-text-secondary, #605e5c);
}

.grid__expand-button:hover {
  background-color: var(--theme-hover-background, #f3f2f1);
  color: var(--grid-primary-color, var(--theme-primary-color, #0078d4));
}

.grid__expand-button:focus {
  outline: 2px solid var(--grid-primary-color, var(--theme-primary-color, #0078d4));
  outline-offset: 1px;
}

.grid__expand-icon {
  display: inline-block;
  transition: transform 0.2s ease;
  font-size: 10px;
  line-height: 1;
}

.grid__expand-icon--expanded {
  transform: rotate(90deg);
}

/* Expanded Content */
.grid__expanded-content {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--theme-bg-secondary, #f8f9fa);
  border: 1px solid var(--grid-border-color, var(--theme-border-color, #e1e5e9));
  border-top: none;
  padding: var(--theme-spacing-md, 16px);
  z-index: 10;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .grid__row {
    min-height: var(--grid-row-height, 48px);
    font-size: var(--theme-font-size-sm, 14px);
  }
  
  .grid__cell--selection {
    width: 36px;
    min-width: 36px;
    max-width: 36px;
  }
  
  .grid__cell--expand {
    width: 28px;
    min-width: 28px;
    max-width: 28px;
  }
  
  .grid__expand-button {
    padding: 1px;
  }
  
  .grid__expand-icon {
    font-size: 8px;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .grid__row {
    border-bottom-width: 2px;
  }
  
  .grid__row:focus {
    outline-width: 3px;
  }
  
  .grid__row--selected {
    border: 2px solid var(--grid-primary-color, var(--theme-primary-color, #0078d4));
  }
  
  .grid__cell--selection,
  .grid__cell--expand {
    border-right-width: 2px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .grid__row {
    transition: none;
  }
  
  .grid__expand-icon {
    transition: none;
  }
  
  .grid__expand-button {
    transition: none;
  }
}

/* Print Styles */
@media print {
  .grid__row {
    break-inside: avoid;
    border-bottom: 1px solid #000;
  }
  
  .grid__row:hover {
    background-color: transparent;
  }
  
  .grid__row--selected {
    background-color: #f0f0f0;
  }
  
  .grid__cell--selection,
  .grid__cell--expand {
    display: none;
  }
  
  .grid__expanded-content {
    position: static;
    box-shadow: none;
    border: 1px solid #000;
    margin-top: 5px;
  }
}

/* Touch Devices */
@media (hover: none) and (pointer: coarse) {
  .grid__row {
    min-height: 48px; /* Minimum touch target */
  }
  
  .grid__cell--selection {
    width: 44px;
    min-width: 44px;
    max-width: 44px;
  }
  
  .grid__cell--expand {
    width: 40px;
    min-width: 40px;
    max-width: 40px;
  }
  
  .grid__expand-button {
    padding: 6px;
    min-width: 32px;
    min-height: 32px;
  }
  
  .grid__cell--selection input[type="checkbox"],
  .grid__cell--selection input[type="radio"] {
    transform: scale(1.3);
  }
}

/* Dark Mode */
@media (prefers-color-scheme: dark) {
  .grid__row {
    background: var(--theme-bg-primary, #2d2d2d);
    border-bottom-color: var(--theme-border-color, #404040);
  }
  
  .grid__row:hover {
    background-color: var(--theme-hover-background, #333333);
  }
  
  .grid__row--selected {
    background-color: var(--theme-selected-background, #0d47a1) !important;
  }
  
  .grid__row--group {
    background-color: var(--theme-bg-secondary, #404040);
  }
  
  .grid__expanded-content {
    background: var(--theme-bg-secondary, #404040);
    border-color: var(--theme-border-color, #404040);
  }
}
