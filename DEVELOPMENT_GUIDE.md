# Development Guide

This guide provides comprehensive instructions for developing with the CRM React Apps monorepo.

## Table of Contents

- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Code Quality](#code-quality)
- [Testing](#testing)
- [Theme Development](#theme-development)
- [Component Development](#component-development)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm 9+
- Git
- VS Code (recommended)

### Initial Setup

```bash
# Clone the repository
git clone <repository-url>
cd crm-react-apps

# Install dependencies
npm install

# Verify setup
npm run type-check
npm run lint
npm test
```

### Development Environment

```bash
# Start development servers
npm run dev:transcript-and-summary    # Port 5173
npm run dev:if-party-master          # Port 5174

# Start with specific deployment mode
npm run dev:transcript-and-summary:webresource    # D365 mode
npm run dev:transcript-and-summary:standalone     # Standalone mode
```

## Development Workflow

### 1. Feature Development

```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Make changes
# ... develop your feature

# Run quality checks
npm run quality:check

# Fix any issues
npm run quality:fix

# Run tests
npm test

# Commit changes
git add .
git commit -m "feat: add your feature description"

# Push and create PR
git push origin feature/your-feature-name
```

### 2. Code Organization

```
src/
├── components/          # Reusable UI components
├── services/           # Business logic and API calls
├── utils/              # Pure utility functions
├── styles/             # CSS and theme files
├── types/              # TypeScript type definitions
└── __tests__/          # Test files
```

### 3. Import Conventions

```typescript
// External libraries first
import React from 'react';
import { format } from 'date-fns';

// Internal imports with aliases
import { Button, LoadingSpinner } from '@shared/components';
import { useTheme } from '@shared/services/theme';
import { formatDate, logger } from '@shared/utils';

// Relative imports last
import './MyComponent.css';
```

## Code Quality

### ESLint Configuration

The project uses comprehensive ESLint rules:

```bash
# Check for linting errors
npm run lint

# Fix auto-fixable issues
npm run lint:fix

# Check with zero warnings tolerance
npm run lint:check
```

### Prettier Formatting

```bash
# Format all files
npm run format

# Check formatting
npm run format:check
```

### TypeScript

```bash
# Type checking
npm run type-check

# Strict type checking
npm run type-check:strict
```

### Quality Gates

Before committing, ensure all quality checks pass:

```bash
npm run quality:check
```

This runs:
- TypeScript type checking
- ESLint with zero warnings
- Prettier format checking

## Testing

### Test Structure

```typescript
// Component test example
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider, ThemeMode } from '@shared/services/theme';
import { MyComponent } from './MyComponent';

const renderWithTheme = (component, theme = ThemeMode.CRM) => {
  return render(
    <ThemeProvider defaultTheme={theme}>
      {component}
    </ThemeProvider>
  );
};

describe('MyComponent', () => {
  test('renders correctly', () => {
    renderWithTheme(<MyComponent />);
    expect(screen.getByTestId('my-component')).toBeInTheDocument();
  });

  test('works with different themes', () => {
    const { rerender } = renderWithTheme(<MyComponent />, ThemeMode.CRM);
    expect(screen.getByTestId('my-component')).toHaveClass('theme-crm');

    rerender(
      <ThemeProvider defaultTheme={ThemeMode.MFE}>
        <MyComponent />
      </ThemeProvider>
    );
    expect(screen.getByTestId('my-component')).toHaveClass('theme-mfe');
  });
});
```

### Running Tests

```bash
# Run all tests
npm test

# Run with coverage
npm run test:coverage

# Run specific test suites
npm run test:shared
npm run test:theme
npm run test:components

# Watch mode
npm run test:watch

# CI mode
npm run test:ci
```

### Coverage Requirements

- Global: 80% coverage (branches, functions, lines, statements)
- Shared package: 85% coverage
- Exclude: config files, test files, type definitions

## Theme Development

### Creating Theme-Aware Components

```typescript
import { useThemeStyles } from '@shared/services/theme';

export const MyComponent = () => {
  const { getThemeClass, getCSSVariable } = useThemeStyles();
  
  return (
    <div className={getThemeClass('my-component')}>
      <p style={{ color: getCSSVariable('--theme-primary') }}>
        Theme-aware content
      </p>
    </div>
  );
};
```

### CSS Custom Properties

```css
/* Use theme variables */
.my-component {
  background: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  border: 1px solid var(--theme-border-primary);
  font-family: var(--theme-font-family);
  padding: var(--theme-spacing-md);
  border-radius: var(--theme-border-radius);
}

/* Theme-specific overrides */
[data-theme="crm"] .my-component {
  /* CRM-specific styles */
}

[data-theme="mfe"] .my-component {
  /* MFE-specific styles */
}
```

### Testing Themes

```typescript
test('component adapts to theme changes', () => {
  const { rerender } = renderWithTheme(<MyComponent />, ThemeMode.CRM);
  
  // Test CRM theme
  expect(document.documentElement).toHaveAttribute('data-theme', 'crm');
  
  // Switch to MFE theme
  rerender(
    <ThemeProvider defaultTheme={ThemeMode.MFE}>
      <MyComponent />
    </ThemeProvider>
  );
  
  expect(document.documentElement).toHaveAttribute('data-theme', 'mfe');
});
```

## Component Development

### Component Template

```typescript
import React from 'react';
import { useThemeStyles } from '@shared/services/theme';

export interface MyComponentProps {
  /**
   * Component description
   */
  children: React.ReactNode;
  /**
   * Optional variant
   */
  variant?: 'primary' | 'secondary';
  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * MyComponent description
 */
export const MyComponent: React.FC<MyComponentProps> = ({
  children,
  variant = 'primary',
  className = '',
}) => {
  const { getThemeClass } = useThemeStyles();
  
  const classes = [
    'my-component',
    `my-component-${variant}`,
    getThemeClass('my-component'),
    className,
  ].filter(Boolean).join(' ');
  
  return (
    <div className={classes} data-testid="my-component">
      {children}
    </div>
  );
};
```

### Component Checklist

- [ ] TypeScript interface with JSDoc comments
- [ ] Theme-aware styling with CSS custom properties
- [ ] Proper accessibility attributes (ARIA, roles, etc.)
- [ ] Test coverage including theme variations
- [ ] Responsive design considerations
- [ ] Error boundary handling if needed
- [ ] Loading states for async operations

## Best Practices

### 1. TypeScript

```typescript
// Use strict types
interface User {
  id: string;
  name: string;
  email: string;
}

// Avoid any, use unknown instead
const parseData = (data: unknown): User => {
  // Type guards and validation
};

// Use utility types
type PartialUser = Partial<User>;
type UserEmail = Pick<User, 'email'>;
```

### 2. React Patterns

```typescript
// Use functional components with hooks
const MyComponent = () => {
  const [state, setState] = useState(initialState);
  
  // Memoize expensive calculations
  const expensiveValue = useMemo(() => {
    return computeExpensiveValue(props);
  }, [props.dependency]);
  
  // Memoize callbacks
  const handleClick = useCallback(() => {
    // Handle click
  }, [dependency]);
  
  return <div>{/* JSX */}</div>;
};
```

### 3. Error Handling

```typescript
// Service layer
export const fetchData = async (): Promise<Data> => {
  try {
    const response = await apiClient.get('/data');
    return response.data;
  } catch (error) {
    logger.error('Failed to fetch data', error);
    throw new Error('Data fetch failed');
  }
};

// Component layer
const MyComponent = () => {
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        const result = await fetchData();
        setData(result);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };
    
    loadData();
  }, []);
  
  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;
  if (!data) return <EmptyState />;
  
  return <DataDisplay data={data} />;
};
```

### 4. Performance

```typescript
// Lazy load components
const LazyComponent = React.lazy(() => import('./LazyComponent'));

// Use React.memo for expensive components
const ExpensiveComponent = React.memo(({ data }) => {
  return <div>{/* Expensive rendering */}</div>;
});

// Optimize re-renders
const OptimizedComponent = ({ items, onItemClick }) => {
  return (
    <div>
      {items.map(item => (
        <MemoizedItem
          key={item.id}
          item={item}
          onClick={onItemClick}
        />
      ))}
    </div>
  );
};
```

## Troubleshooting

### Common Issues

#### 1. Theme Not Loading

**Problem**: Components don't show theme styles

**Solution**:
```typescript
// Ensure ThemeProvider wraps your app
<ThemeProvider>
  <App />
</ThemeProvider>

// Check CSS imports
import '@shared/styles/index.css';

// Verify data-theme attribute
console.log(document.documentElement.getAttribute('data-theme'));
```

#### 2. Import Errors

**Problem**: Module not found errors

**Solution**:
```bash
# Check path aliases in tsconfig.json
# Restart TypeScript server in VS Code
# Verify file exists and exports are correct
```

#### 3. Test Failures

**Problem**: Tests fail unexpectedly

**Solution**:
```bash
# Clear Jest cache
npx jest --clearCache

# Run specific test
npm test -- --testNamePattern="test name"

# Debug mode
npm test -- --verbose
```

#### 4. Build Errors

**Problem**: Build fails in production

**Solution**:
```bash
# Check TypeScript errors
npm run type-check

# Check for unused imports
npm run lint

# Clean and rebuild
npm run clean
npm install
npm run build:all
```

### Getting Help

1. Check this documentation first
2. Search existing issues in the repository
3. Check the troubleshooting section in README.md
4. Create a detailed issue with:
   - Steps to reproduce
   - Expected vs actual behavior
   - Environment details
   - Error messages/logs

### Development Tools

#### VS Code Extensions

- TypeScript and JavaScript Language Features
- ESLint
- Prettier
- Auto Rename Tag
- Bracket Pair Colorizer
- GitLens

#### Browser Extensions

- React Developer Tools
- Redux DevTools (if using Redux)

#### Useful Commands

```bash
# Package analysis
npm ls                          # List installed packages
npm outdated                    # Check for outdated packages
npm audit                       # Security audit

# Development
npm run dev:transcript-and-summary -- --host  # Expose dev server
npm run build:all -- --watch    # Watch mode build

# Debugging
npm test -- --detectOpenHandles # Find hanging processes
npm run type-check -- --listFiles # List TypeScript files
```
