/**
 * Authentication Switching Example
 * 
 * Comprehensive demonstration of authentication across deployment modes:
 * - Web resource authentication (Dynamics 365 Xrm context)
 * - Standalone MFE authentication (MSAL)
 * - Auth state management and user login/logout flows
 * - Auth-aware components that render differently based on authentication status
 * - Integration of theme switching with authentication context
 */

import React, { useState, useEffect, createContext, useContext } from 'react';

// Mock deployment modes
enum DeploymentMode {
  WEB_RESOURCE = 'web_resource',
  EMBEDDED_SPA = 'embedded_spa',
  STANDALONE_MFE = 'standalone_mfe'
}

// Mock theme modes
enum ThemeMode {
  CRM = 'crm',
  MFE = 'mfe'
}

// Mock user interface
interface User {
  id: string;
  name: string;
  email: string;
  roles: string[];
  avatar?: string;
}

// Mock auth context interface
interface AuthContextValue {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  deploymentMode: DeploymentMode;
  login: (credentials?: any) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
}

// Mock auth context
const AuthContext = createContext<AuthContextValue | null>(null);

// Mock auth hook
const useAuth = (): AuthContextValue => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};

// Mock auth provider
const AuthProvider: React.FC<{
  children: React.ReactNode;
  deploymentMode: DeploymentMode;
}> = ({ children, deploymentMode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const isAuthenticated = user !== null;

  const login = async (credentials?: any) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simulate different auth flows based on deployment mode
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (deploymentMode === DeploymentMode.WEB_RESOURCE || deploymentMode === DeploymentMode.EMBEDDED_SPA) {
        // Simulate Xrm authentication
        const mockUser: User = {
          id: 'xrm-user-123',
          name: 'John Dynamics',
          email: '<EMAIL>',
          roles: ['system_user', 'sales_manager'],
          avatar: '👤'
        };
        setUser(mockUser);
      } else {
        // Simulate MSAL authentication
        const mockUser: User = {
          id: 'msal-user-456',
          name: 'Jane Champion',
          email: '<EMAIL>',
          roles: ['user', 'admin'],
          avatar: '👩‍💼'
        };
        setUser(mockUser);
      }
    } catch (err) {
      setError('Authentication failed');
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      setUser(null);
    } catch (err) {
      setError('Logout failed');
    } finally {
      setIsLoading(false);
    }
  };

  const refreshToken = async () => {
    // Mock token refresh
    console.log('Token refreshed for', deploymentMode);
  };

  const value: AuthContextValue = {
    user,
    isAuthenticated,
    isLoading,
    error,
    deploymentMode,
    login,
    logout,
    refreshToken
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Auth-aware header component
const AuthHeader: React.FC = () => {
  const { user, isAuthenticated, deploymentMode, logout, isLoading } = useAuth();
  
  const getDeploymentModeLabel = (mode: DeploymentMode) => {
    switch (mode) {
      case DeploymentMode.WEB_RESOURCE:
        return '🏢 Web Resource';
      case DeploymentMode.EMBEDDED_SPA:
        return '📱 Embedded SPA';
      case DeploymentMode.STANDALONE_MFE:
        return '🌐 Standalone MFE';
      default:
        return mode;
    }
  };

  return (
    <header style={{
      padding: '16px 24px',
      backgroundColor: 'var(--theme-bg-primary, #ffffff)',
      borderBottom: '1px solid var(--theme-border-primary, #e0e0e0)',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
    }}>
      <div>
        <h1 style={{ 
          margin: 0, 
          color: 'var(--theme-text-primary, #333)',
          fontSize: '24px'
        }}>
          Auth Switching Demo
        </h1>
        <p style={{ 
          margin: '4px 0 0 0', 
          color: 'var(--theme-text-secondary, #666)',
          fontSize: '14px'
        }}>
          {getDeploymentModeLabel(deploymentMode)}
        </p>
      </div>
      
      {isAuthenticated && user && (
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <div style={{ textAlign: 'right' }}>
            <div style={{ 
              fontWeight: 'bold',
              color: 'var(--theme-text-primary, #333)'
            }}>
              {user.avatar} {user.name}
            </div>
            <div style={{ 
              fontSize: '12px',
              color: 'var(--theme-text-secondary, #666)'
            }}>
              {user.email}
            </div>
            <div style={{ 
              fontSize: '11px',
              color: 'var(--theme-text-tertiary, #999)'
            }}>
              Roles: {user.roles.join(', ')}
            </div>
          </div>
          <button
            onClick={logout}
            disabled={isLoading}
            style={{
              padding: '8px 16px',
              backgroundColor: 'var(--theme-primary, #0078d4)',
              color: 'white',
              border: 'none',
              borderRadius: 'var(--theme-border-radius, 4px)',
              cursor: isLoading ? 'not-allowed' : 'pointer',
              opacity: isLoading ? 0.6 : 1
            }}
          >
            {isLoading ? 'Logging out...' : 'Logout'}
          </button>
        </div>
      )}
    </header>
  );
};

// Login form component
const LoginForm: React.FC = () => {
  const { login, isLoading, error, deploymentMode } = useAuth();
  const [credentials, setCredentials] = useState({ username: '', password: '' });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await login(credentials);
  };

  const getAuthMethodDescription = () => {
    switch (deploymentMode) {
      case DeploymentMode.WEB_RESOURCE:
        return 'Authentication via Dynamics 365 Xrm context. User is automatically authenticated when accessing the web resource.';
      case DeploymentMode.EMBEDDED_SPA:
        return 'Authentication via Dynamics 365 embedded context with additional SPA-specific token management.';
      case DeploymentMode.STANDALONE_MFE:
        return 'Authentication via Microsoft Authentication Library (MSAL) with Azure AD integration.';
      default:
        return 'Unknown authentication method.';
    }
  };

  return (
    <div style={{
      maxWidth: '400px',
      margin: '48px auto',
      padding: '32px',
      backgroundColor: 'var(--theme-bg-primary, #ffffff)',
      border: '1px solid var(--theme-border-primary, #e0e0e0)',
      borderRadius: 'var(--theme-border-radius, 8px)',
      boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
    }}>
      <h2 style={{ 
        textAlign: 'center',
        marginBottom: '24px',
        color: 'var(--theme-text-primary, #333)'
      }}>
        Please Sign In
      </h2>
      
      <div style={{
        padding: '16px',
        backgroundColor: 'var(--theme-bg-secondary, #f5f5f5)',
        borderRadius: 'var(--theme-border-radius, 4px)',
        marginBottom: '24px',
        fontSize: '14px',
        color: 'var(--theme-text-secondary, #666)'
      }}>
        <strong>Auth Method:</strong><br />
        {getAuthMethodDescription()}
      </div>

      <form onSubmit={handleSubmit}>
        <div style={{ marginBottom: '16px' }}>
          <label style={{ 
            display: 'block',
            marginBottom: '8px',
            color: 'var(--theme-text-primary, #333)',
            fontWeight: 'bold'
          }}>
            Username:
          </label>
          <input
            type="text"
            value={credentials.username}
            onChange={(e) => setCredentials(prev => ({ ...prev, username: e.target.value }))}
            style={{
              width: '100%',
              padding: '12px',
              border: '1px solid var(--theme-border-primary, #ccc)',
              borderRadius: 'var(--theme-border-radius, 4px)',
              fontSize: '14px'
            }}
            placeholder={deploymentMode === DeploymentMode.STANDALONE_MFE ? '<EMAIL>' : '<EMAIL>'}
          />
        </div>
        
        <div style={{ marginBottom: '24px' }}>
          <label style={{ 
            display: 'block',
            marginBottom: '8px',
            color: 'var(--theme-text-primary, #333)',
            fontWeight: 'bold'
          }}>
            Password:
          </label>
          <input
            type="password"
            value={credentials.password}
            onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
            style={{
              width: '100%',
              padding: '12px',
              border: '1px solid var(--theme-border-primary, #ccc)',
              borderRadius: 'var(--theme-border-radius, 4px)',
              fontSize: '14px'
            }}
            placeholder="Enter password"
          />
        </div>

        {error && (
          <div style={{
            padding: '12px',
            backgroundColor: '#fee',
            color: '#c33',
            border: '1px solid #fcc',
            borderRadius: 'var(--theme-border-radius, 4px)',
            marginBottom: '16px',
            fontSize: '14px'
          }}>
            {error}
          </div>
        )}

        <button
          type="submit"
          disabled={isLoading}
          style={{
            width: '100%',
            padding: '12px',
            backgroundColor: 'var(--theme-primary, #0078d4)',
            color: 'white',
            border: 'none',
            borderRadius: 'var(--theme-border-radius, 4px)',
            fontSize: '16px',
            fontWeight: 'bold',
            cursor: isLoading ? 'not-allowed' : 'pointer',
            opacity: isLoading ? 0.6 : 1
          }}
        >
          {isLoading ? 'Signing in...' : 'Sign In'}
        </button>
      </form>
    </div>
  );
};

// Auth-aware dashboard component
const AuthDashboard: React.FC = () => {
  const { user, deploymentMode } = useAuth();

  const getFeaturesByDeployment = () => {
    switch (deploymentMode) {
      case DeploymentMode.WEB_RESOURCE:
        return [
          '🔗 Direct Dynamics 365 integration',
          '📊 Access to CRM data via Xrm.WebApi',
          '🔐 Automatic SSO with Dynamics 365',
          '📱 Embedded in Dynamics 365 interface',
          '⚡ Fast data access (same domain)'
        ];
      case DeploymentMode.EMBEDDED_SPA:
        return [
          '🔗 Dynamics 365 integration with SPA benefits',
          '📊 Enhanced data management capabilities',
          '🔐 Hybrid authentication approach',
          '📱 Modern SPA user experience',
          '⚡ Optimized for complex workflows'
        ];
      case DeploymentMode.STANDALONE_MFE:
        return [
          '🌐 Independent web application',
          '📊 External API integration',
          '🔐 Azure AD authentication via MSAL',
          '📱 Full responsive design',
          '⚡ Modern web technologies'
        ];
      default:
        return [];
    }
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '24px',
        marginBottom: '32px'
      }}>
        {/* User Info Card */}
        <div style={{
          padding: '24px',
          backgroundColor: 'var(--theme-bg-primary, #ffffff)',
          border: '1px solid var(--theme-border-primary, #e0e0e0)',
          borderRadius: 'var(--theme-border-radius, 8px)',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <h3 style={{
            marginTop: 0,
            color: 'var(--theme-text-primary, #333)',
            borderBottom: '2px solid var(--theme-primary, #0078d4)',
            paddingBottom: '8px'
          }}>
            👤 User Information
          </h3>
          <div style={{ fontSize: '14px', lineHeight: '1.6' }}>
            <p><strong>Name:</strong> {user?.name}</p>
            <p><strong>Email:</strong> {user?.email}</p>
            <p><strong>User ID:</strong> {user?.id}</p>
            <p><strong>Roles:</strong> {user?.roles.join(', ')}</p>
          </div>
        </div>

        {/* Deployment Info Card */}
        <div style={{
          padding: '24px',
          backgroundColor: 'var(--theme-bg-primary, #ffffff)',
          border: '1px solid var(--theme-border-primary, #e0e0e0)',
          borderRadius: 'var(--theme-border-radius, 8px)',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <h3 style={{
            marginTop: 0,
            color: 'var(--theme-text-primary, #333)',
            borderBottom: '2px solid var(--theme-primary, #0078d4)',
            paddingBottom: '8px'
          }}>
            🚀 Deployment Mode
          </h3>
          <div style={{ fontSize: '14px', lineHeight: '1.6' }}>
            <p><strong>Current Mode:</strong> {deploymentMode}</p>
            <p><strong>Features:</strong></p>
            <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
              {getFeaturesByDeployment().map((feature, index) => (
                <li key={index} style={{ marginBottom: '4px' }}>{feature}</li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      {/* Role-based content */}
      <div style={{
        padding: '24px',
        backgroundColor: 'var(--theme-bg-secondary, #f5f5f5)',
        borderRadius: 'var(--theme-border-radius, 8px)',
        marginBottom: '24px'
      }}>
        <h3 style={{
          marginTop: 0,
          color: 'var(--theme-text-primary, #333)'
        }}>
          🔐 Role-Based Access
        </h3>

        {user?.roles.includes('admin') && (
          <div style={{
            padding: '16px',
            backgroundColor: '#e8f5e8',
            border: '1px solid #4caf50',
            borderRadius: 'var(--theme-border-radius, 4px)',
            marginBottom: '16px'
          }}>
            <h4 style={{ margin: '0 0 8px 0', color: '#2e7d32' }}>👑 Admin Access</h4>
            <p style={{ margin: 0, fontSize: '14px' }}>
              You have administrative privileges. You can manage users, configure settings, and access all features.
            </p>
          </div>
        )}

        {user?.roles.includes('sales_manager') && (
          <div style={{
            padding: '16px',
            backgroundColor: '#e3f2fd',
            border: '1px solid #2196f3',
            borderRadius: 'var(--theme-border-radius, 4px)',
            marginBottom: '16px'
          }}>
            <h4 style={{ margin: '0 0 8px 0', color: '#1976d2' }}>📈 Sales Manager Access</h4>
            <p style={{ margin: 0, fontSize: '14px' }}>
              You can view sales reports, manage team performance, and access CRM analytics.
            </p>
          </div>
        )}

        {user?.roles.includes('user') && (
          <div style={{
            padding: '16px',
            backgroundColor: '#fff3e0',
            border: '1px solid #ff9800',
            borderRadius: 'var(--theme-border-radius, 4px)',
            marginBottom: '16px'
          }}>
            <h4 style={{ margin: '0 0 8px 0', color: '#f57c00' }}>👤 Standard User Access</h4>
            <p style={{ margin: 0, fontSize: '14px' }}>
              You have access to standard features and can view your assigned data.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

// Deployment mode switcher for demo purposes
const DeploymentModeSwitcher: React.FC<{
  currentMode: DeploymentMode;
  onModeChange: (mode: DeploymentMode) => void;
}> = ({ currentMode, onModeChange }) => {
  return (
    <div style={{
      padding: '16px',
      backgroundColor: 'var(--theme-bg-secondary, #f5f5f5)',
      borderBottom: '1px solid var(--theme-border-primary, #e0e0e0)',
      textAlign: 'center'
    }}>
      <label style={{
        marginRight: '12px',
        color: 'var(--theme-text-primary, #333)',
        fontWeight: 'bold'
      }}>
        Demo Deployment Mode:
      </label>
      <select
        value={currentMode}
        onChange={(e) => onModeChange(e.target.value as DeploymentMode)}
        style={{
          padding: '8px 12px',
          borderRadius: 'var(--theme-border-radius, 4px)',
          border: '1px solid var(--theme-border-primary, #ccc)',
          backgroundColor: 'var(--theme-bg-primary, #ffffff)',
          color: 'var(--theme-text-primary, #333)'
        }}
      >
        <option value={DeploymentMode.WEB_RESOURCE}>🏢 Web Resource</option>
        <option value={DeploymentMode.EMBEDDED_SPA}>📱 Embedded SPA</option>
        <option value={DeploymentMode.STANDALONE_MFE}>🌐 Standalone MFE</option>
      </select>
    </div>
  );
};

// Main auth switching example component
export const AuthSwitchingExample: React.FC = () => {
  const [deploymentMode, setDeploymentMode] = useState<DeploymentMode>(DeploymentMode.STANDALONE_MFE);

  // Apply theme based on deployment mode
  useEffect(() => {
    const theme = deploymentMode === DeploymentMode.STANDALONE_MFE ? ThemeMode.MFE : ThemeMode.CRM;
    document.documentElement.setAttribute('data-theme', theme);

    // Apply CSS custom properties based on theme
    const root = document.documentElement;
    if (theme === ThemeMode.CRM) {
      root.style.setProperty('--theme-primary', '#0078d4');
      root.style.setProperty('--theme-bg-primary', '#ffffff');
      root.style.setProperty('--theme-bg-secondary', '#f3f2f1');
      root.style.setProperty('--theme-text-primary', '#323130');
      root.style.setProperty('--theme-text-secondary', '#605e5c');
      root.style.setProperty('--theme-border-primary', '#d2d0ce');
      root.style.setProperty('--theme-border-radius', '2px');
    } else {
      root.style.setProperty('--theme-primary', '#5e10b1');
      root.style.setProperty('--theme-bg-primary', '#ffffff');
      root.style.setProperty('--theme-bg-secondary', '#f8f9fa');
      root.style.setProperty('--theme-text-primary', '#212529');
      root.style.setProperty('--theme-text-secondary', '#6c757d');
      root.style.setProperty('--theme-border-primary', '#dee2e6');
      root.style.setProperty('--theme-border-radius', '8px');
    }
  }, [deploymentMode]);

  return (
    <AuthProvider deploymentMode={deploymentMode}>
      <div style={{
        minHeight: '100vh',
        backgroundColor: 'var(--theme-bg-secondary, #f5f5f5)',
        fontFamily: 'var(--theme-font-family, system-ui)'
      }}>
        <DeploymentModeSwitcher
          currentMode={deploymentMode}
          onModeChange={setDeploymentMode}
        />

        <AuthContent />
      </div>
    </AuthProvider>
  );
};

// Auth content wrapper
const AuthContent: React.FC = () => {
  const { isAuthenticated } = useAuth();

  return (
    <>
      <AuthHeader />
      {isAuthenticated ? <AuthDashboard /> : <LoginForm />}
    </>
  );
};

export default AuthSwitchingExample;
