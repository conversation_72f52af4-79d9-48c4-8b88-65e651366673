/* Grid Body */
.grid__body {
  flex: 1;
  overflow: auto;
  background: var(--theme-bg-primary, #ffffff);
  position: relative;
}

.grid__virtual-container {
  position: relative;
  width: 100%;
}

/* Stacked Layout for Mobile */
.grid__body--stacked {
  padding: var(--theme-spacing-md, 16px);
  gap: var(--theme-spacing-md, 16px);
  display: flex;
  flex-direction: column;
}

.grid__row-card {
  background: var(--theme-bg-primary, #ffffff);
  border: 1px solid var(--grid-border-color, var(--theme-border-color, #e1e5e9));
  border-radius: var(--theme-border-radius, 8px);
  padding: var(--theme-spacing-md, 16px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

.grid__row-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.grid__row-card--selected {
  border-color: var(--grid-primary-color, var(--theme-primary-color, #0078d4));
  background-color: var(--grid-selected-bg, var(--theme-selected-background, #deecf9));
}

.grid__card-field {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--theme-spacing-sm, 8px) 0;
  border-bottom: 1px solid var(--theme-border-light, #f3f2f1);
  min-height: 32px;
}

.grid__card-field:last-child {
  border-bottom: none;
}

.grid__card-field::before {
  content: attr(data-label);
  font-weight: 600;
  color: var(--theme-text-secondary, #605e5c);
  font-size: var(--theme-font-size-sm, 12px);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  flex: 0 0 auto;
  margin-right: var(--theme-spacing-md, 16px);
  min-width: 80px;
}

.grid__card-field > * {
  flex: 1;
  text-align: right;
}

/* Loading State */
.grid__body--loading {
  pointer-events: none;
  opacity: 0.6;
}

.grid__body--loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

/* Empty State */
.grid__body--empty {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: var(--theme-text-muted, #8a8886);
  font-style: italic;
}

/* Scrollbar Styling */
.grid__body::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.grid__body::-webkit-scrollbar-track {
  background: var(--theme-bg-secondary, #f3f2f1);
  border-radius: 4px;
}

.grid__body::-webkit-scrollbar-thumb {
  background: var(--grid-border-color, var(--theme-border-color, #e1e5e9));
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.grid__body::-webkit-scrollbar-thumb:hover {
  background: var(--theme-text-muted, #8a8886);
}

.grid__body::-webkit-scrollbar-corner {
  background: var(--theme-bg-secondary, #f3f2f1);
}

/* Firefox Scrollbar */
.grid__body {
  scrollbar-width: thin;
  scrollbar-color: var(--grid-border-color, var(--theme-border-color, #e1e5e9)) var(--theme-bg-secondary, #f3f2f1);
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .grid__row-card {
    border-width: 2px;
  }
  
  .grid__row-card--selected {
    border-width: 3px;
  }
  
  .grid__card-field {
    border-bottom-width: 2px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .grid__row-card {
    transition: none;
  }
  
  .grid__row-card:hover {
    transform: none;
  }
}

/* Print Styles */
@media print {
  .grid__body {
    overflow: visible;
    height: auto;
  }
  
  .grid__row-card {
    box-shadow: none;
    border: 1px solid #000;
    break-inside: avoid;
    margin-bottom: 10px;
  }
  
  .grid__card-field::before {
    color: #000;
  }
}

/* Touch Devices */
@media (hover: none) and (pointer: coarse) {
  .grid__row-card {
    padding: var(--theme-spacing-lg, 20px);
  }
  
  .grid__card-field {
    padding: var(--theme-spacing-md, 12px) 0;
    min-height: 44px; /* Minimum touch target */
  }
  
  .grid__card-field::before {
    font-size: var(--theme-font-size-md, 14px);
  }
}

/* Dark Mode */
@media (prefers-color-scheme: dark) {
  .grid__row-card {
    background: var(--theme-bg-primary, #2d2d2d);
    border-color: var(--theme-border-color, #404040);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
  
  .grid__row-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
  }
  
  .grid__card-field {
    border-bottom-color: var(--theme-border-light, #404040);
  }
}
