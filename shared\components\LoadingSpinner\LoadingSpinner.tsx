import React from 'react';
import { useThemeStyles } from '../../services/theme/themeContext';

export interface LoadingSpinnerProps {
  /**
   * Size of the spinner
   */
  size?: 'small' | 'medium' | 'large';
  /**
   * Loading message to display
   */
  message?: string;
  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * Loading spinner component with theme support
 */
export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'medium',
  message,
  className = '',
}) => {
  const { getThemeClass } = useThemeStyles();

  const classes = [
    'loading-spinner',
    `loading-spinner-${size}`,
    getThemeClass('loading-spinner'),
    className,
  ].filter(Boolean).join(' ');

  return (
    <div
      className="loading-spinner-container"
      data-testid="loading-spinner"
      role="status"
      aria-label="Loading"
    >
      <div className={classes}>
        <div className="loading-spinner-circle" />
      </div>
      {message && (
        <div className="loading-spinner-message">
          {message}
        </div>
      )}
    </div>
  );
};
