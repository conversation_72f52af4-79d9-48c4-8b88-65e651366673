/* Grid Pagination Container */
.grid__pagination-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--theme-spacing-md, 16px) var(--theme-spacing-lg, 24px);
  background: var(--theme-bg-primary, #ffffff);
  border-top: 1px solid var(--grid-border-color, var(--theme-border-color, #e1e5e9));
  gap: var(--theme-spacing-md, 16px);
  flex-wrap: wrap;
}

.grid__pagination {
  flex: 1;
}

/* Page Size Selector */
.grid__page-size-selector {
  display: flex;
  align-items: center;
  gap: var(--theme-spacing-sm, 8px);
  font-size: var(--theme-font-size-sm, 14px);
}

.grid__page-size-label {
  color: var(--theme-text-secondary, #605e5c);
  font-weight: 500;
  white-space: nowrap;
}

.grid__page-size-select {
  padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
  border: 1px solid var(--grid-border-color, var(--theme-border-color, #e1e5e9));
  border-radius: var(--theme-border-radius-sm, 2px);
  font-size: var(--theme-font-size-sm, 12px);
  background: var(--theme-input-background, #ffffff);
  color: var(--theme-text-color, #323130);
  outline: none;
  cursor: pointer;
  min-width: 60px;
}

.grid__page-size-select:focus {
  border-color: var(--grid-primary-color, var(--theme-primary-color, #0078d4));
  box-shadow: 0 0 0 1px var(--grid-primary-color, var(--theme-primary-color, #0078d4));
}

/* Responsive Design */
@media (max-width: 768px) {
  .grid__pagination-container {
    flex-direction: column;
    align-items: stretch;
    gap: var(--theme-spacing-sm, 8px);
    padding: var(--theme-spacing-sm, 8px) var(--theme-spacing-md, 16px);
  }
  
  .grid__page-size-selector {
    justify-content: center;
    order: -1;
  }
}

@media (max-width: 480px) {
  .grid__pagination-container {
    padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
  }
  
  .grid__page-size-selector {
    font-size: var(--theme-font-size-xs, 12px);
  }
  
  .grid__page-size-select {
    font-size: var(--theme-font-size-xs, 11px);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .grid__pagination-container {
    border-top-width: 2px;
  }
  
  .grid__page-size-select:focus {
    box-shadow: 0 0 0 2px var(--grid-primary-color, var(--theme-primary-color, #0078d4));
  }
}

/* Print Styles */
@media print {
  .grid__pagination-container {
    display: none;
  }
}

/* Dark Mode */
@media (prefers-color-scheme: dark) {
  .grid__pagination-container {
    background: var(--theme-bg-primary, #2d2d2d);
    border-top-color: var(--theme-border-color, #404040);
  }
  
  .grid__page-size-select {
    background: var(--theme-input-background, #2d2d2d);
    border-color: var(--theme-border-color, #404040);
    color: var(--theme-text-color, #ffffff);
  }
}
