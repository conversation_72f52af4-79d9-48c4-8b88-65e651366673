import { useState, useEffect, useMemo } from 'react';
import { GridColumn } from '../types';

export interface ResponsiveConfig {
  breakpoints: Record<string, number>;
  hiddenColumns: Record<string, string[]>;
  stackedLayout?: boolean;
  compactMode?: boolean;
}

export interface ResponsiveState {
  currentBreakpoint: string;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  screenWidth: number;
  visibleColumns: string[];
  shouldStack: boolean;
  shouldCompact: boolean;
}

const defaultBreakpoints = {
  mobile: 768,
  tablet: 1024,
  desktop: 1200
};

const defaultHiddenColumns = {
  mobile: ['notes', 'callLength', 'timeOfCall'],
  tablet: ['notes']
};

export function useResponsive<T = any>(
  columns: GridColumn<T>[],
  config: ResponsiveConfig = {
    breakpoints: defaultBreakpoints,
    hiddenColumns: defaultHiddenColumns
  }
): ResponsiveState {
  const [screenWidth, setScreenWidth] = useState(() => {
    if (typeof window !== 'undefined') {
      return window.innerWidth;
    }
    return 1200; // Default for SSR
  });

  // Update screen width on resize
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      setScreenWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Calculate current breakpoint
  const currentBreakpoint = useMemo(() => {
    const breakpoints = { ...defaultBreakpoints, ...config.breakpoints };
    
    if (screenWidth < breakpoints.mobile) return 'mobile';
    if (screenWidth < breakpoints.tablet) return 'tablet';
    if (screenWidth < breakpoints.desktop) return 'desktop';
    return 'wide';
  }, [screenWidth, config.breakpoints]);

  // Calculate device type flags
  const deviceFlags = useMemo(() => ({
    isMobile: currentBreakpoint === 'mobile',
    isTablet: currentBreakpoint === 'tablet',
    isDesktop: currentBreakpoint === 'desktop' || currentBreakpoint === 'wide'
  }), [currentBreakpoint]);

  // Calculate visible columns based on breakpoint
  const visibleColumns = useMemo(() => {
    const hiddenColumns = config.hiddenColumns || defaultHiddenColumns;
    const hiddenForBreakpoint = hiddenColumns[currentBreakpoint] || [];
    
    return columns
      .filter(column => !hiddenForBreakpoint.includes(String(column.key)))
      .map(column => String(column.key));
  }, [columns, currentBreakpoint, config.hiddenColumns]);

  // Calculate layout flags
  const layoutFlags = useMemo(() => ({
    shouldStack: config.stackedLayout && deviceFlags.isMobile,
    shouldCompact: config.compactMode && (deviceFlags.isMobile || deviceFlags.isTablet)
  }), [config.stackedLayout, config.compactMode, deviceFlags]);

  return {
    currentBreakpoint,
    ...deviceFlags,
    screenWidth,
    visibleColumns,
    ...layoutFlags
  };
}

// Hook for responsive column widths
export function useResponsiveColumns<T = any>(
  columns: GridColumn<T>[],
  containerWidth: number,
  responsive: ResponsiveState
): GridColumn<T>[] {
  return useMemo(() => {
    if (!responsive.isDesktop) {
      // On mobile/tablet, adjust column widths
      const visibleCols = columns.filter(col => 
        responsive.visibleColumns.includes(String(col.key))
      );

      const totalFlexColumns = visibleCols.filter(col => col.flex).length;
      const fixedWidth = visibleCols
        .filter(col => !col.flex && col.width)
        .reduce((sum, col) => sum + (typeof col.width === 'number' ? col.width : 0), 0);

      const availableWidth = containerWidth - fixedWidth;
      const flexWidth = totalFlexColumns > 0 ? availableWidth / totalFlexColumns : 0;

      return visibleCols.map(col => ({
        ...col,
        width: col.flex ? flexWidth : col.width || 120
      }));
    }

    return columns.filter(col => 
      responsive.visibleColumns.includes(String(col.key))
    );
  }, [columns, containerWidth, responsive]);
}

// Hook for responsive grid features
export function useResponsiveFeatures(responsive: ResponsiveState) {
  return useMemo(() => {
    const features = {
      // Disable some features on mobile for better performance
      virtualization: !responsive.isMobile,
      
      // Simplify pagination on mobile
      pagination: {
        showPageInfo: !responsive.isMobile,
        showPageSizeSelector: responsive.isDesktop,
        showQuickJumper: responsive.isDesktop,
        maxPageNumbers: responsive.isMobile ? 3 : responsive.isTablet ? 5 : 7
      },
      
      // Adjust filtering UI
      filtering: {
        columnFilters: responsive.isDesktop,
        globalSearch: true,
        advancedFilters: responsive.isDesktop,
        filterPresets: responsive.isDesktop
      },
      
      // Adjust selection UI
      selection: {
        showSelectAll: !responsive.isMobile,
        showBulkActions: !responsive.isMobile
      },
      
      // Adjust editing behavior
      editing: {
        inlineEditing: responsive.isDesktop,
        popupEditing: !responsive.isDesktop
      },
      
      // Export options
      export: {
        showInToolbar: responsive.isDesktop,
        showInMenu: !responsive.isDesktop
      }
    };

    return features;
  }, [responsive]);
}

// Hook for touch gestures on mobile
export function useTouchGestures(
  onSwipeLeft?: () => void,
  onSwipeRight?: () => void,
  onPinch?: (scale: number) => void
) {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    let startX = 0;
    let startY = 0;
    let startDistance = 0;

    const handleTouchStart = (e: TouchEvent) => {
      if (e.touches.length === 1) {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
      } else if (e.touches.length === 2 && onPinch) {
        const dx = e.touches[0].clientX - e.touches[1].clientX;
        const dy = e.touches[0].clientY - e.touches[1].clientY;
        startDistance = Math.sqrt(dx * dx + dy * dy);
      }
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (e.touches.length === 2 && onPinch && startDistance > 0) {
        const dx = e.touches[0].clientX - e.touches[1].clientX;
        const dy = e.touches[0].clientY - e.touches[1].clientY;
        const distance = Math.sqrt(dx * dx + dy * dy);
        const scale = distance / startDistance;
        onPinch(scale);
      }
    };

    const handleTouchEnd = (e: TouchEvent) => {
      if (e.changedTouches.length === 1) {
        const endX = e.changedTouches[0].clientX;
        const endY = e.changedTouches[0].clientY;
        const deltaX = endX - startX;
        const deltaY = endY - startY;

        // Check if it's a horizontal swipe
        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
          if (deltaX > 0 && onSwipeRight) {
            onSwipeRight();
          } else if (deltaX < 0 && onSwipeLeft) {
            onSwipeLeft();
          }
        }
      }
    };

    document.addEventListener('touchstart', handleTouchStart);
    document.addEventListener('touchmove', handleTouchMove);
    document.addEventListener('touchend', handleTouchEnd);

    return () => {
      document.removeEventListener('touchstart', handleTouchStart);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };
  }, [onSwipeLeft, onSwipeRight, onPinch]);
}

// Utility function to get responsive styles
export function getResponsiveStyles(responsive: ResponsiveState) {
  return {
    container: {
      fontSize: responsive.isMobile ? '14px' : '16px',
      padding: responsive.isMobile ? '8px' : '16px'
    },
    
    header: {
      height: responsive.isMobile ? '40px' : '48px',
      fontSize: responsive.isMobile ? '12px' : '14px'
    },
    
    row: {
      height: responsive.isMobile ? '48px' : '56px',
      padding: responsive.isMobile ? '8px' : '12px'
    },
    
    cell: {
      padding: responsive.isMobile ? '4px 8px' : '8px 12px',
      fontSize: responsive.isMobile ? '12px' : '14px'
    },
    
    pagination: {
      buttonSize: responsive.isMobile ? 'small' : 'medium',
      spacing: responsive.isMobile ? '4px' : '8px'
    }
  };
}
