import React from 'react';
import { useThemeStyles } from '../../services/theme/themeContext';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /**
   * Button contents
   */
  children: React.ReactNode;
  /**
   * Button variant
   */
  variant?: 'primary' | 'secondary' | 'outline' | 'danger';
  /**
   * Button size
   */
  size?: 'small' | 'medium' | 'large';
  /**
   * Loading state
   */
  loading?: boolean;
}

/**
 * Primary UI component for user interaction
 */
export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'medium',
  loading = false,
  disabled = false,
  className = '',
  type = 'button',
  ...props
}) => {
  const { getThemeClass } = useThemeStyles();

  const classes = [
    'button',
    `button-${variant}`,
    `button-${size}`,
    loading && 'button-loading',
    getThemeClass('button'),
    className,
  ].filter(Boolean).join(' ');

  const isDisabled = disabled || loading;

  return (
    <button
      type={type}
      className={classes}
      disabled={isDisabled}
      {...props}
    >
      {loading ? (
        <span className="button-spinner" aria-hidden="true">
          ⟳
        </span>
      ) : null}
      <span className={loading ? 'button-text-loading' : 'button-text'}>
        {children}
      </span>
    </button>
  );
};
