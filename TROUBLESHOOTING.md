# Troubleshooting Guide

This guide helps you diagnose and resolve common issues in the CRM React Apps monorepo.

## Table of Contents

- [Quick Diagnostics](#quick-diagnostics)
- [Installation Issues](#installation-issues)
- [Development Server Issues](#development-server-issues)
- [Build Issues](#build-issues)
- [Theme Issues](#theme-issues)
- [Testing Issues](#testing-issues)
- [TypeScript Issues](#typescript-issues)
- [Performance Issues](#performance-issues)
- [Deployment Issues](#deployment-issues)

## Quick Diagnostics

Run these commands to quickly identify common issues:

```bash
# Check Node.js and npm versions
node --version    # Should be 18+
npm --version     # Should be 9+

# Verify installation
npm run type-check
npm run lint:check
npm test:simple

# Check for common issues
npm ls --depth=0  # List top-level dependencies
npm audit         # Security vulnerabilities
```

## Installation Issues

### Issue: `npm install` fails with dependency conflicts

**Symptoms:**
- ERESOLVE errors during installation
- Peer dependency warnings
- Module not found errors

**Solutions:**

```bash
# Option 1: Use legacy peer deps
npm install --legacy-peer-deps

# Option 2: Clear cache and reinstall
npm cache clean --force
rm -rf node_modules package-lock.json
npm install

# Option 3: Check Node.js version
nvm use 18  # or install Node.js 18+
npm install
```

### Issue: Workspace dependencies not resolving

**Symptoms:**
- Cannot find module '@shared/...'
- Import errors in IDE

**Solutions:**

```bash
# Reinstall all workspace dependencies
npm run install:all

# Check workspace configuration
npm ls --workspaces

# Verify tsconfig.json paths
cat tsconfig.base.json | grep -A 10 "paths"
```

## Development Server Issues

### Issue: Development server won't start

**Symptoms:**
- Port already in use
- Server crashes on startup
- Module resolution errors

**Solutions:**

```bash
# Check if ports are in use
netstat -an | grep :5173
netstat -an | grep :5174

# Kill processes using ports
npx kill-port 5173 5174

# Start with different port
npm run dev:transcript-and-summary -- --port 3000

# Clear Vite cache
rm -rf node_modules/.vite
npm run dev:transcript-and-summary
```

### Issue: Hot reload not working

**Symptoms:**
- Changes don't reflect in browser
- Manual refresh required

**Solutions:**

```bash
# Check file watching limits (Linux/Mac)
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# Restart development server
npm run dev:transcript-and-summary

# Check for file path issues
# Ensure no spaces or special characters in project path
```

## Build Issues

### Issue: TypeScript compilation errors

**Symptoms:**
- Build fails with TS errors
- Type checking errors

**Solutions:**

```bash
# Run type checking separately
npm run type-check

# Check for strict mode issues
npm run type-check:strict

# Common fixes:
# 1. Add missing type annotations
# 2. Handle null/undefined cases
# 3. Fix import/export statements

# Example fixes:
// Before
const data = getData();  // Type 'unknown'

// After
const data: UserData = getData() as UserData;
// or
const data = getData() as UserData;
```

### Issue: Build succeeds but runtime errors

**Symptoms:**
- Build completes successfully
- Application crashes in browser
- Console errors about missing modules

**Solutions:**

```bash
# Check for dynamic imports
# Ensure all dynamic imports are properly handled

# Check bundle analysis
npm run build:transcript-and-summary
# Inspect dist/ folder for missing files

# Common issues:
# 1. Missing CSS imports
# 2. Incorrect asset paths
# 3. Environment variable issues
```

## Theme Issues

### Issue: Themes not switching

**Symptoms:**
- Theme switcher doesn't work
- Styles don't change
- CSS variables not updating

**Solutions:**

```typescript
// Check ThemeProvider setup
import { ThemeProvider } from '@shared/services/theme';

function App() {
  return (
    <ThemeProvider enableAutoDetection enablePersistence>
      <YourApp />
    </ThemeProvider>
  );
}

// Verify CSS imports
import '@shared/styles/index.css';

// Check data-theme attribute
console.log(document.documentElement.getAttribute('data-theme'));

// Debug theme context
const { currentTheme, error } = useTheme();
console.log('Current theme:', currentTheme);
console.log('Theme error:', error);
```

### Issue: CSS custom properties not working

**Symptoms:**
- Styles show default values
- CSS variables not resolving
- Theme colors not applying

**Solutions:**

```css
/* Check CSS variable syntax */
.my-component {
  /* Correct */
  color: var(--theme-primary);
  
  /* Incorrect */
  color: var(--primary-color);  /* Wrong variable name */
}

/* Verify theme-aware selectors */
[data-theme="crm"] .my-component {
  --theme-primary: #0078d4;
}

[data-theme="mfe"] .my-component {
  --theme-primary: #5e10b1;
}
```

### Issue: Theme persistence not working

**Symptoms:**
- Theme resets on page reload
- localStorage not saving theme

**Solutions:**

```typescript
// Check localStorage permissions
try {
  localStorage.setItem('test', 'test');
  localStorage.removeItem('test');
  console.log('localStorage available');
} catch (e) {
  console.error('localStorage not available:', e);
}

// Verify ThemeProvider configuration
<ThemeProvider enablePersistence storageKey="my-app-theme">
  <App />
</ThemeProvider>

// Debug storage
console.log(localStorage.getItem('crm-app-theme'));
```

## Testing Issues

### Issue: Tests failing unexpectedly

**Symptoms:**
- Previously passing tests now fail
- Inconsistent test results
- Timeout errors

**Solutions:**

```bash
# Clear Jest cache
npx jest --clearCache

# Run tests with verbose output
npm test -- --verbose

# Run specific test file
npm test -- shared/__tests__/theme.test.tsx

# Debug test environment
npm test -- --detectOpenHandles
npm test -- --forceExit
```

### Issue: Theme-related test failures

**Symptoms:**
- Theme tests fail
- CSS custom properties not mocked
- DOM attributes not set

**Solutions:**

```typescript
// Check test setup
// Ensure jest.setup.js is properly configured

// Mock CSS custom properties
window.getComputedStyle = jest.fn().mockImplementation(() => ({
  getPropertyValue: jest.fn().mockImplementation((property) => {
    const mockValues = {
      '--theme-primary': '#0078d4',
      '--theme-secondary': '#106ebe',
    };
    return mockValues[property] || '';
  }),
}));

// Verify theme provider in tests
const renderWithTheme = (component, theme = ThemeMode.CRM) => {
  return render(
    <ThemeProvider defaultTheme={theme} enableAutoDetection={false}>
      {component}
    </ThemeProvider>
  );
};
```

## TypeScript Issues

### Issue: Module resolution errors

**Symptoms:**
- Cannot find module '@shared/...'
- Path mapping not working
- IDE shows import errors

**Solutions:**

```bash
# Check tsconfig.json configuration
cat tsconfig.base.json

# Verify path mappings
{
  "baseUrl": ".",
  "paths": {
    "@shared/*": ["./shared/*"],
    "@shared/components": ["./shared/components"],
    "@shared/services": ["./shared/services"]
  }
}

# Restart TypeScript server in VS Code
# Cmd/Ctrl + Shift + P -> "TypeScript: Restart TS Server"

# Check file extensions
# Ensure imports include proper extensions for non-TS files
import './styles.css';  // Include .css
import data from './data.json';  // Include .json
```

### Issue: Strict mode errors

**Symptoms:**
- Null/undefined errors
- Implicit any errors
- Property access errors

**Solutions:**

```typescript
// Handle null/undefined cases
// Before
const user = getUser();
const name = user.name;  // Error: user might be null

// After
const user = getUser();
const name = user?.name ?? 'Unknown';

// Use type guards
function isUser(obj: any): obj is User {
  return obj && typeof obj.name === 'string';
}

if (isUser(data)) {
  // data is now typed as User
  console.log(data.name);
}

// Add explicit types
// Before
const data = getData();  // Type: any

// After
const data: UserData = getData();
// or
const data = getData() as UserData;
```

## Performance Issues

### Issue: Slow development server

**Symptoms:**
- Long startup times
- Slow hot reload
- High memory usage

**Solutions:**

```bash
# Check system resources
top  # or Task Manager on Windows

# Reduce file watching
# Add to .gitignore:
node_modules/
dist/
coverage/
*.log

# Optimize Vite configuration
// vite.config.ts
export default defineConfig({
  server: {
    fs: {
      strict: false
    }
  },
  optimizeDeps: {
    include: ['react', 'react-dom']
  }
});
```

### Issue: Large bundle sizes

**Symptoms:**
- Slow page loads
- Large JavaScript files
- Poor performance metrics

**Solutions:**

```bash
# Analyze bundle
npm run build:transcript-and-summary
# Check dist/ folder sizes

# Use code splitting
const LazyComponent = React.lazy(() => import('./LazyComponent'));

// Optimize imports
// Before
import * as lodash from 'lodash';

// After
import { debounce } from 'lodash';
// or
import debounce from 'lodash/debounce';
```

## Deployment Issues

### Issue: Web resource deployment fails

**Symptoms:**
- Build succeeds but deployment fails
- Files not uploading correctly
- Runtime errors in Dynamics 365

**Solutions:**

```bash
# Check web resource build
npm run build:all:webresource

# Verify single file output
ls -la apps/*/dist/webresource/

# Check file sizes (Dynamics 365 limits)
# Max file size: 5MB for web resources

# Common issues:
# 1. Source maps too large (disabled in production)
# 2. Multiple chunks (should be single file)
# 3. Incorrect MIME types
```

### Issue: Standalone deployment issues

**Symptoms:**
- 404 errors for assets
- Routing not working
- API calls failing

**Solutions:**

```bash
# Check base URL configuration
# vite.config.ts
export default defineConfig({
  base: '/your-app-path/',  // Set correct base path
});

# Verify API endpoints
# Check CORS configuration
# Ensure proper authentication setup

# Test locally
npm run build:transcript-and-summary:standalone
npm run preview:transcript-and-summary
```

## Getting Additional Help

### Debug Information to Collect

When reporting issues, include:

```bash
# System information
node --version
npm --version
git --version

# Project information
npm ls --depth=0
npm run type-check 2>&1
npm run lint 2>&1

# Error logs
# Include full error messages and stack traces
# Browser console errors
# Network tab information (for API issues)
```

### Useful Debug Commands

```bash
# Verbose logging
DEBUG=* npm run dev:transcript-and-summary

# Memory usage
node --max-old-space-size=4096 npm run build:all

# Network debugging
npm run dev:transcript-and-summary -- --host 0.0.0.0

# Bundle analysis
npm run build:transcript-and-summary -- --analyze
```

### Community Resources

1. Check existing GitHub issues
2. Search Stack Overflow with relevant tags
3. Consult official documentation:
   - React: https://react.dev/
   - TypeScript: https://www.typescriptlang.org/
   - Vite: https://vitejs.dev/
   - Jest: https://jestjs.io/

### Creating Bug Reports

Include in your bug report:
1. **Environment**: OS, Node.js version, npm version
2. **Steps to reproduce**: Exact commands and actions
3. **Expected behavior**: What should happen
4. **Actual behavior**: What actually happens
5. **Error messages**: Full error text and stack traces
6. **Screenshots**: If UI-related
7. **Additional context**: Any other relevant information
