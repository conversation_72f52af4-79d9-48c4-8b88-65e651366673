# CRM React Apps Monorepo

A modern multi-app React monorepo built with Vite, TypeScript, and shared packages. Includes legacy PCF components for Dynamics 365 integration.

## 🏗️ Architecture

This monorepo contains:

- **Modern React Apps** (`apps/`) - Independent React 18 applications with Vite
- **Shared Packages** (`shared/`) - Reusable components, services, and utilities with deployment abstraction
- **PCF Components** (`pcf/`) - Dynamics 365 Power Platform Components (React 16)

### 🔄 Migration-Ready Architecture

The monorepo supports seamless migration from Dynamics 365 web resources to standalone SPAs:

- **Deployment Context Detection** - Automatically detects runtime environment
- **Authentication Abstraction** - Unified auth layer supporting both D365 implicit auth and MSAL
- **API Client Abstraction** - Switches between Xrm.WebApi and external Dataverse API calls
- **Runtime Theme Switching** - Context-aware theming with Dynamics CRM and ZB Champion themes
- **Configuration-Driven** - Environment-based switching with minimal code changes

## 📁 Project Structure

```
crm-react-apps/
├── apps/
│   ├── transcript-and-summary/     # Transcript and Summary App
│   └── if-party-master/           # IF Party Master App
├── pcf/
│   ├── contact-timeline-control/   # Dynamics 365 PCF Control
│   └── shared/                    # PCF-specific shared components
├── shared/
│   ├── components/                # Reusable React components
│   ├── services/                  # API clients and business logic
│   └── utils/                     # Pure utility functions
├── tsconfig.base.json             # Shared TypeScript configuration
├── package.json                   # Workspace configuration
└── README.md
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- npm 9+

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd crm-react-apps

# Install all dependencies
npm install

# Optional: Copy environment variables template
cp .env.example .env
```

### Development

Start individual apps in development mode:

```bash
# Default mode (auto-detects deployment context)
npm run dev:transcript-and-summary
npm run dev:if-party-master

# Specific deployment modes
npm run dev:transcript-and-summary:webresource    # D365 web resource mode
npm run dev:transcript-and-summary:standalone     # Standalone SPA mode
npm run dev:if-party-master:webresource
npm run dev:if-party-master:standalone
```

Apps will be available at:
- Transcript and Summary: http://localhost:5173
- IF Party Master: http://localhost:5174

### Building

Build individual apps:

```bash
# Default builds
npm run build:transcript-and-summary
npm run build:if-party-master
npm run build:shared
npm run build:all

# Deployment-specific builds
npm run build:all:webresource                    # For D365 web resource deployment
npm run build:all:standalone                     # For standalone SPA deployment
npm run build:transcript-and-summary:webresource
npm run build:transcript-and-summary:standalone
npm run build:if-party-master:webresource
npm run build:if-party-master:standalone
```

### Preview Production Builds

```bash
npm run preview:transcript-and-summary
npm run preview:if-party-master
```

## 📦 Import Aliases

Use these aliases to import shared code:

```typescript
// Components
import { Button } from '@shared/components/Button';
import { DatePicker } from '@shared/components';

// Services (deployment-aware)
import { getApiClient } from '@shared/services/api/apiFactory';
import { useAuth } from '@shared/services';

// Configuration
import { getDeploymentConfig, isWebResourceMode } from '@shared/config';

// Utils
import { formatDate } from '@shared/utils/formatDate';
import { logger } from '@shared/utils';
```

## 🔄 Migration Support

The monorepo supports migration from Dynamics 365 web resources to standalone SPAs:

```typescript
// Automatic deployment detection
import { getDeploymentConfig } from '@shared/config';
const config = getDeploymentConfig();

// Unified authentication (works in both modes)
import { useAuth } from '@shared/services';
const { user, isAuthenticated, login } = useAuth();

// Deployment-aware API client
import { getApiClient } from '@shared/services';
const apiClient = await getApiClient();
const contacts = await apiClient.retrieveMultipleRecords('contact');

// Runtime theme switching
import { ThemeProvider, useTheme, ThemeSwitcher } from '@shared/services';
const { currentTheme, switchTheme } = useTheme();
```

See [MIGRATION_GUIDE.md](./MIGRATION_GUIDE.md) for detailed migration instructions.
See [THEME_GUIDE.md](./THEME_GUIDE.md) for theme system documentation.
See [CUSTOM_THEME_INTEGRATION_GUIDE.md](./CUSTOM_THEME_INTEGRATION_GUIDE.md) for custom theme integration.

## 🎨 Theme Switching System

The monorepo includes a sophisticated runtime theme switching system that automatically adapts to deployment contexts while supporting manual theme switching for development and testing.

### 🏗️ Theme Architecture

The theme system uses **CSS custom properties** (CSS variables) combined with **React Context API** for efficient runtime theme switching without page reloads:

- **CSS Custom Properties**: Enable instant theme switching by updating CSS variables
- **React Context**: Provides theme state management and hooks for components
- **Deployment Context Detection**: Automatically selects appropriate theme based on environment
- **Theme Persistence**: Remembers user theme preferences across sessions

### 🎯 Available Themes

#### CRM Theme (`ThemeMode.CRM`)
- **Usage**: Web resource and embedded SPA deployments
- **Design**: Dynamics 365 enterprise styling with conservative approach
- **Colors**: Microsoft blue (#0078d4) with Segoe UI font family
- **Controls**: 32px standard height for accessibility compliance
- **Target**: Enterprise users within Dynamics 365 environment

#### MFE Theme (`ThemeMode.MFE`)
- **Usage**: Standalone micro frontend deployments
- **Design**: Modern ZB Champion styling with touch-friendly interface
- **Colors**: ZB Champion purple (#5e10b1) with RNHouseSans font family
- **Controls**: 44px touch-friendly height with 16px border radius
- **Target**: External users accessing standalone applications

### 🚀 Quick Start with Themes

#### 1. Basic Theme Setup

```tsx
import React from 'react';
import { ThemeProvider } from '@shared/services/theme';
import '@shared/styles/index.css'; // Import theme styles

function App() {
  return (
    <ThemeProvider enableAutoDetection={true} enablePersistence={true}>
      <YourAppContent />
    </ThemeProvider>
  );
}
```

#### 2. Using Theme Hooks

```tsx
import { useTheme, useThemeStyles, ThemeMode } from '@shared/services/theme';

function MyComponent() {
  const { currentTheme, switchTheme } = useTheme();
  const { getThemeClass, getCSSVariable } = useThemeStyles();

  const primaryColor = getCSSVariable('--theme-primary');
  const cardClass = getThemeClass('card');

  return (
    <div className={cardClass} style={{ color: primaryColor }}>
      <p>Current theme: {currentTheme}</p>
      <button onClick={() => switchTheme(ThemeMode.MFE)}>
        Switch to MFE Theme
      </button>
    </div>
  );
}
```

#### 3. Theme-Aware Components

```tsx
import { useThemeStyles, ThemeMode } from '@shared/services/theme';

function ThemedButton({ children, onClick }) {
  const { getThemeStyle } = useThemeStyles();

  const buttonStyles = getThemeStyle({
    [ThemeMode.CRM]: {
      backgroundColor: '#0078d4',
      borderRadius: '2px',
      height: '32px'
    },
    [ThemeMode.MFE]: {
      backgroundColor: '#5e10b1',
      borderRadius: '16px',
      height: '44px'
    }
  });

  return (
    <button style={buttonStyles} onClick={onClick}>
      {children}
    </button>
  );
}
```

### 🔄 Automatic Theme Detection

The system automatically detects the deployment context and applies the appropriate theme:

```typescript
// Deployment Context → Theme Mapping
DeploymentMode.WEB_RESOURCE    → ThemeMode.CRM  // Dynamics 365 web resource
DeploymentMode.EMBEDDED_SPA    → ThemeMode.CRM  // Embedded in Dynamics 365
DeploymentMode.STANDALONE_MFE  → ThemeMode.MFE  // Standalone application
```

#### Environment-Based Detection

```bash
# Development with specific themes
npm run dev:transcript-and-summary:webresource   # → CRM theme
npm run dev:transcript-and-summary:standalone     # → MFE theme

# URL parameter override (development only)
http://localhost:5173?themeMode=mfe              # Force MFE theme
http://localhost:5173?themeMode=crm              # Force CRM theme
```

### 🎨 App-Specific Theme Customizations

Individual apps can add their own theme customizations while respecting the base theme structure:

#### 1. Create App-Specific Theme File

```css
/* apps/my-app/src/styles/app-theme.css */

/* CRM mode customizations */
[data-theme="crm"] .my-app-header {
  background-color: var(--theme-primary);
  border-radius: var(--crm-border-radius, 2px);
  padding: var(--crm-spacing-md, 16px);
}

/* MFE mode customizations */
[data-theme="mfe"] .my-app-header {
  background-color: var(--theme-primary);
  border-radius: var(--mfe-border-radius, 16px);
  padding: var(--mfe-spacing-lg, 24px);
}

/* Custom properties that work in both themes */
.my-app-card {
  background: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  border: 1px solid var(--theme-border-primary);
  font-family: var(--theme-font-family);
}
```

#### 2. Import in App

```tsx
// apps/my-app/src/main.tsx
import '@shared/styles/index.css';     // Base themes
import './styles/app-theme.css';       // App-specific overrides
```

#### 3. Use Theme-Aware Classes

```tsx
function AppHeader() {
  const { getThemeClass } = useThemeStyles();

  return (
    <header className={`my-app-header ${getThemeClass('header')}`}>
      <h1>My Application</h1>
    </header>
  );
}
```

### 🧪 Testing Theme Switching

#### Development Testing

```bash
# Test both themes in development
npm run dev:transcript-and-summary:webresource   # CRM theme
npm run dev:transcript-and-summary:standalone     # MFE theme

# Manual theme switching via URL
http://localhost:5173?themeMode=crm&enableThemeSwitching=true
```

#### Component Testing

```tsx
import { render } from '@testing-library/react';
import { ThemeProvider, ThemeMode } from '@shared/services/theme';

function renderWithTheme(component, theme = ThemeMode.CRM) {
  return render(
    <ThemeProvider defaultTheme={theme} enableAutoDetection={false}>
      {component}
    </ThemeProvider>
  );
}

test('component renders correctly in CRM theme', () => {
  const { container } = renderWithTheme(<MyComponent />, ThemeMode.CRM);
  expect(container.firstChild).toHaveAttribute('data-theme', 'crm');
});
```

### 📱 Responsive Design

Both themes include responsive breakpoints:

```css
/* CRM Theme - Conservative breakpoints */
[data-theme="crm"] {
  --theme-mobile-breakpoint: 768px;
  --theme-tablet-breakpoint: 1024px;
}

/* MFE Theme - Touch-friendly breakpoints */
[data-theme="mfe"] {
  --theme-mobile-breakpoint: 840px;  /* ZB Champion standard */
  --theme-tablet-breakpoint: 1200px;
}

/* Responsive usage */
@media (max-width: var(--theme-mobile-breakpoint)) {
  .my-component {
    padding: var(--theme-spacing-sm);
    font-size: 0.875rem;
  }
}
```

### 🔧 Advanced Customization

#### Custom CSS Properties

```css
/* Define custom properties in your theme files */
[data-theme="crm"] {
  --my-app-primary: #0078d4;
  --my-app-spacing: 16px;
  --my-app-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

[data-theme="mfe"] {
  --my-app-primary: #5e10b1;
  --my-app-spacing: 24px;
  --my-app-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}
```

#### Theme-Specific Logic

```tsx
import { useCurrentTheme, useIsTheme, ThemeMode } from '@shared/services/theme';

function AdvancedComponent() {
  const currentTheme = useCurrentTheme();
  const isMFETheme = useIsTheme(ThemeMode.MFE);

  // Theme-specific behavior
  const showAdvancedFeatures = isMFETheme;
  const animationDuration = currentTheme === ThemeMode.MFE ? 300 : 150;

  return (
    <div>
      {showAdvancedFeatures && <AdvancedFeaturePanel />}
      <AnimatedComponent duration={animationDuration} />
    </div>
  );
}
```

### 🎯 Best Practices

#### ✅ Do's

- Use CSS custom properties for all theme-dependent values
- Leverage theme hooks (`useTheme`, `useThemeStyles`) for dynamic behavior
- Test components in both CRM and MFE themes
- Use `getThemeClass()` for theme-aware CSS classes
- Respect the base theme structure when adding customizations

#### ❌ Don'ts

- Avoid hardcoded colors or dimensions in components
- Don't bypass the theme system with inline styles
- Don't assume a specific theme will always be active
- Avoid theme-specific imports or conditional CSS imports
- Don't modify the base theme files directly

### 🔍 Troubleshooting Themes

#### Theme Not Loading

1. **Check CSS imports**:
   ```tsx
   import '@shared/styles/index.css';
   ```

2. **Verify ThemeProvider wrapper**:
   ```tsx
   <ThemeProvider>
     <App />
   </ThemeProvider>
   ```

3. **Inspect data-theme attribute**:
   ```html
   <html data-theme="mfe">
   <body data-theme="mfe">
   ```

#### CSS Variables Not Working

1. **Use correct variable names**:
   ```css
   /* ✅ Correct */
   color: var(--theme-primary);

   /* ❌ Incorrect */
   color: var(--primary-color);
   ```

2. **Check theme-aware selectors**:
   ```css
   /* ✅ Theme-aware */
   [data-theme="mfe"] .my-component {
     color: var(--theme-primary);
   }

   /* ❌ Not theme-aware */
   .my-component {
     color: #5e10b1;
   }
   ```

For detailed theme system documentation, see [THEME_GUIDE.md](./THEME_GUIDE.md).

## 🔧 PCF Development

PCF components use React 16.14.0 for Dynamics 365 compatibility:

```bash
# Build PCF component
npm run build:pcf

# The built component will be in pcf/contact-timeline-control/dist/
```

### PCF Deployment

1. Build the PCF component: `npm run build:pcf`
2. Use Power Platform CLI to package and deploy
3. Import into your Dynamics 365 environment

## 📦 Web Resource Build Configuration

The build system is configured to generate single-file outputs for Dynamics 365 web resources:

### Web Resource Build

```bash
# Build for web resource deployment (single files)
npm run build:transcript-and-summary:webresource
npm run build:if-party-master:webresource

# Build all apps for web resource
npm run build:all:webresource
```

### Web Resource Output

When `VITE_DEPLOYMENT_MODE=web_resource`, the build produces:

```
dist/webresource/
├── index.html                    # Single HTML file
├── [app-name].js                # Single JavaScript bundle
└── [app-name].css               # Single CSS bundle (if styles exist)
```

### Web Resource Features

- **Single file output**: No code splitting for easy web resource upload
- **Optimized bundles**: Terser minification with console.log removal
- **No source maps**: Cleaner production files
- **Inlined imports**: All dynamic imports are bundled inline

### Standalone Build

```bash
# Build for standalone deployment (optimized chunks)
npm run build:transcript-and-summary:standalone
npm run build:if-party-master:standalone
```

Standalone builds use code splitting and optimized chunking for better performance in external hosting scenarios.

## 🧪 Testing

Comprehensive testing setup with Jest, React Testing Library, and theme-aware testing utilities.

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests with coverage threshold enforcement
npm run test:coverage:threshold

# Run specific test suites
npm run test:shared
npm run test:shared:coverage
npm run test:transcript-and-summary
npm run test:if-party-master

# Run specific test files
npm run test:simple
npm run test:theme
npm run test:components
npm run test:utils

# CI/CD pipeline tests
npm run test:ci

# Type checking
npm run type-check
npm run type-check:strict

# Code quality checks
npm run quality:check
npm run quality:fix
```

### Testing Features

- **Comprehensive Coverage**: 80% global coverage, 85% for shared package
- **Theme Testing**: Specialized utilities for testing theme switching
- **Component Testing**: React Testing Library with theme providers
- **Utility Testing**: Validation, formatting, and logging utilities
- **Integration Testing**: Cross-component and service integration
- **Accessibility Testing**: Built-in a11y testing with jest-dom
- **Mock Utilities**: Browser APIs, localStorage, CSS custom properties

### Testing Setup

The project provides comprehensive testing utilities:

- **Theme-aware testing**: Mock theme providers and CSS custom properties
- **Browser API mocks**: localStorage, sessionStorage, matchMedia, ResizeObserver
- **Jest configuration**: Multi-project setup with TypeScript support
- **CSS mocking**: Automatic CSS import mocking for tests
- **Coverage reporting**: HTML, LCOV, and JSON summary reports

### Example Tests

```tsx
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider, ThemeMode } from '@shared/services/theme';
import { Button } from '@shared/components';

// Theme-aware testing helper
const renderWithTheme = (component, theme = ThemeMode.CRM) => {
  return render(
    <ThemeProvider defaultTheme={theme} enableAutoDetection={false}>
      {component}
    </ThemeProvider>
  );
};

describe('Button Component', () => {
  test('renders with theme classes', () => {
    renderWithTheme(<Button>Click me</Button>);

    const button = screen.getByRole('button', { name: /click me/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass('button', 'theme-crm');
  });

  test('switches themes correctly', () => {
    const { rerender } = renderWithTheme(<Button>Test</Button>, ThemeMode.CRM);
    expect(screen.getByRole('button')).toHaveClass('theme-crm');

    rerender(
      <ThemeProvider defaultTheme={ThemeMode.MFE} enableAutoDetection={false}>
        <Button>Test</Button>
      </ThemeProvider>
    );
    expect(screen.getByRole('button')).toHaveClass('theme-mfe');
  });

  test('handles loading state', () => {
    renderWithTheme(<Button loading>Loading</Button>);

    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(button).toHaveClass('button-loading');
  });
});
```

### Coverage Requirements

- **Global**: 80% coverage (branches, functions, lines, statements)
- **Shared Package**: 85% coverage
- **Excludes**: Config files, test files, type definitions, build outputs

### Testing Best Practices

- Test components in both CRM and MFE themes
- Use theme-aware testing helpers
- Test accessibility with screen readers in mind
- Mock external dependencies appropriately
- Write descriptive test names and organize with `describe` blocks
- Test error states and edge cases
- Verify loading states and user interactions

## 📝 Adding New Apps

1. Create new directory under `apps/`
2. Copy structure from existing app
3. Update root `package.json` scripts
4. Configure Vite with path aliases

## 📝 Adding New Shared Packages

1. Create new directory under `shared/`
2. Add `index.ts` barrel export
3. Update `tsconfig.base.json` paths if needed
4. Import using `@shared/your-package`

## 🔍 Troubleshooting

### Common Issues

- **Module not found**: Check path aliases in `vite.config.ts` and `tsconfig.json`
- **Type errors**: Ensure `tsconfig.base.json` is extended properly
- **PCF build fails**: Verify React 16 dependencies in PCF package.json
- **Environment variables**: Use `VITE_` prefix for variables accessible in browser code

### Environment Variables

Create a `.env` file in the root directory for custom configuration:

```bash
# API Configuration
VITE_API_BASE_URL=http://localhost:3001/api

# Custom variables (must start with VITE_)
VITE_APP_TITLE=My CRM App
```

### Clean Build

```bash
npm run clean
npm install
npm run build:all
```

## 🤝 Contributing

1. Create feature branch
2. Make changes
3. Test locally with `npm run type-check` and `npm run lint`
4. Submit pull request

## 📚 Documentation

### Comprehensive Guides

- **[API Documentation](./API_DOCUMENTATION.md)** - Complete API reference for components, services, and utilities
- **[Development Guide](./DEVELOPMENT_GUIDE.md)** - Detailed development workflow, best practices, and patterns
- **[Troubleshooting Guide](./TROUBLESHOOTING.md)** - Common issues and solutions
- **[Theme Guide](./THEME_GUIDE.md)** - Theme system documentation and customization
- **[Migration Guide](./MIGRATION_GUIDE.md)** - Migration from legacy systems
- **[Custom Theme Integration](./CUSTOM_THEME_INTEGRATION_GUIDE.md)** - Custom theme development

### Quick References

- **Components**: See [API_DOCUMENTATION.md#components](./API_DOCUMENTATION.md#components)
- **Theme Hooks**: See [API_DOCUMENTATION.md#theme-service](./API_DOCUMENTATION.md#theme-service)
- **Utilities**: See [API_DOCUMENTATION.md#utilities](./API_DOCUMENTATION.md#utilities)
- **Testing**: See [DEVELOPMENT_GUIDE.md#testing](./DEVELOPMENT_GUIDE.md#testing)

## 🚀 Production Readiness

### Code Quality

- **TypeScript Strict Mode**: Full type safety with strict compiler options
- **ESLint**: Comprehensive linting with accessibility and import rules
- **Prettier**: Consistent code formatting across the project
- **Testing**: 80%+ test coverage with theme-aware testing utilities
- **Type Safety**: Complete TypeScript coverage with strict null checks

### Performance

- **Bundle Optimization**: Code splitting and tree shaking for optimal bundle sizes
- **Lazy Loading**: Dynamic imports for non-critical components
- **Memoization**: React.memo and useMemo for expensive operations
- **CSS Optimization**: CSS custom properties for efficient theme switching

### Security

- **Dependency Scanning**: Regular security audits with npm audit
- **Input Validation**: Comprehensive validation utilities
- **XSS Protection**: Proper sanitization and escaping
- **CORS Configuration**: Secure cross-origin resource sharing

### Accessibility

- **ARIA Support**: Comprehensive ARIA attributes and roles
- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Reader Support**: Semantic HTML and proper labeling
- **Color Contrast**: WCAG compliant color schemes in both themes

### Monitoring

- **Error Logging**: Structured logging with different log levels
- **Performance Metrics**: Bundle size analysis and runtime performance
- **Theme Analytics**: Theme usage and switching patterns
- **User Experience**: Loading states and error boundaries

## 🤝 Contributing

### Development Workflow

1. **Fork and Clone**: Fork the repository and clone locally
2. **Install Dependencies**: Run `npm install` to install all dependencies
3. **Create Branch**: Create a feature branch from `main`
4. **Develop**: Make your changes following the development guide
5. **Quality Check**: Run `npm run quality:check` to ensure code quality
6. **Test**: Add tests and ensure `npm run test:coverage` passes
7. **Document**: Update documentation if needed
8. **Submit PR**: Create a pull request with detailed description

### Code Standards

- Follow TypeScript strict mode guidelines
- Use theme-aware components and CSS custom properties
- Write comprehensive tests including theme variations
- Follow accessibility best practices
- Document public APIs with JSDoc comments
- Use semantic commit messages (feat:, fix:, docs:, etc.)

### Review Process

- All PRs require code review
- Automated checks must pass (linting, testing, type checking)
- Documentation updates for new features
- Accessibility review for UI changes
- Performance impact assessment for significant changes

## 📄 License

MIT License - see LICENSE file for details

---

## 📞 Support

- **Documentation**: Check the comprehensive guides above
- **Issues**: Create a GitHub issue with detailed reproduction steps
- **Discussions**: Use GitHub Discussions for questions and ideas
- **Security**: Report security issues privately via email

**Happy coding! 🎉**
