/* Grid Toolbar */
.grid__toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--theme-spacing-md, 16px) var(--theme-spacing-lg, 24px);
  background: var(--theme-bg-secondary, #f8f9fa);
  border-bottom: 1px solid var(--grid-border-color, var(--theme-border-color, #e1e5e9));
  gap: var(--theme-spacing-md, 16px);
  flex-wrap: wrap;
}

.grid__toolbar-left,
.grid__toolbar-right {
  display: flex;
  align-items: center;
  gap: var(--theme-spacing-md, 16px);
  flex-wrap: wrap;
}

.grid__toolbar-left {
  flex: 1;
}

/* Search Container */
.grid__search-container {
  position: relative;
  min-width: 200px;
  max-width: 400px;
  flex: 1;
}

.grid__search-input {
  width: 100%;
  padding: var(--theme-spacing-sm, 8px) var(--theme-spacing-lg, 36px) var(--theme-spacing-sm, 8px) var(--theme-spacing-md, 12px);
  border: 1px solid var(--grid-border-color, var(--theme-border-color, #e1e5e9));
  border-radius: var(--theme-border-radius, 4px);
  font-size: var(--theme-font-size-sm, 14px);
  background: var(--theme-input-background, #ffffff);
  color: var(--theme-text-color, #323130);
  outline: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.grid__search-input:focus {
  border-color: var(--grid-primary-color, var(--theme-primary-color, #0078d4));
  box-shadow: 0 0 0 1px var(--grid-primary-color, var(--theme-primary-color, #0078d4));
}

.grid__search-input::placeholder {
  color: var(--theme-text-muted, #605e5c);
  font-style: italic;
}

.grid__search-icon {
  position: absolute;
  right: var(--theme-spacing-md, 12px);
  top: 50%;
  transform: translateY(-50%);
  color: var(--theme-text-muted, #605e5c);
  pointer-events: none;
  font-size: 14px;
}

/* Filter Summary */
.grid__filter-summary {
  display: flex;
  align-items: center;
  gap: var(--theme-spacing-sm, 8px);
  padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
  background: var(--theme-info-background, #e6f3ff);
  border: 1px solid var(--theme-info-border, #0078d4);
  border-radius: var(--theme-border-radius-sm, 2px);
  font-size: var(--theme-font-size-sm, 12px);
}

.grid__filter-count {
  color: var(--theme-info-text, #0078d4);
  font-weight: 500;
}

.grid__clear-filters {
  font-size: var(--theme-font-size-xs, 11px);
  padding: var(--theme-spacing-xs, 2px) var(--theme-spacing-sm, 6px);
}

/* Selection Info */
.grid__selection-info {
  display: flex;
  align-items: center;
  gap: var(--theme-spacing-sm, 8px);
  padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
  background: var(--theme-success-background, #e6ffe6);
  border: 1px solid var(--theme-success-border, #107c10);
  border-radius: var(--theme-border-radius-sm, 2px);
  font-size: var(--theme-font-size-sm, 12px);
}

.grid__selection-count {
  color: var(--theme-success-text, #107c10);
  font-weight: 500;
}

.grid__deselect-all {
  font-size: var(--theme-font-size-xs, 11px);
  padding: var(--theme-spacing-xs, 2px) var(--theme-spacing-sm, 6px);
}

/* Export Container */
.grid__export-container {
  display: flex;
  align-items: center;
  gap: var(--theme-spacing-sm, 8px);
}

.grid__export-buttons {
  display: flex;
  gap: var(--theme-spacing-xs, 4px);
}

.grid__export-btn {
  font-size: var(--theme-font-size-sm, 12px);
  padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
}

.grid__export-select {
  padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-md, 12px);
  border: 1px solid var(--grid-border-color, var(--theme-border-color, #e1e5e9));
  border-radius: var(--theme-border-radius-sm, 2px);
  font-size: var(--theme-font-size-sm, 12px);
  background: var(--theme-input-background, #ffffff);
  color: var(--theme-text-color, #323130);
  outline: none;
  cursor: pointer;
}

.grid__export-select:focus {
  border-color: var(--grid-primary-color, var(--theme-primary-color, #0078d4));
  box-shadow: 0 0 0 1px var(--grid-primary-color, var(--theme-primary-color, #0078d4));
}

/* Responsive Design */
@media (max-width: 768px) {
  .grid__toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: var(--theme-spacing-sm, 8px);
    padding: var(--theme-spacing-sm, 8px) var(--theme-spacing-md, 16px);
  }
  
  .grid__toolbar-left,
  .grid__toolbar-right {
    justify-content: space-between;
    width: 100%;
  }
  
  .grid__search-container {
    min-width: auto;
    max-width: none;
    flex: 1;
  }
  
  .grid__export-buttons {
    display: none;
  }
  
  .grid__export-select {
    display: inline-block;
  }
  
  .grid__filter-summary,
  .grid__selection-info {
    font-size: var(--theme-font-size-xs, 11px);
  }
}

@media (max-width: 480px) {
  .grid__toolbar-left,
  .grid__toolbar-right {
    flex-direction: column;
    align-items: stretch;
    gap: var(--theme-spacing-xs, 4px);
  }
  
  .grid__search-container {
    order: -1;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .grid__toolbar {
    border-bottom-width: 2px;
  }
  
  .grid__search-input:focus {
    box-shadow: 0 0 0 2px var(--grid-primary-color, var(--theme-primary-color, #0078d4));
  }
  
  .grid__filter-summary,
  .grid__selection-info {
    border-width: 2px;
  }
}

/* Print Styles */
@media print {
  .grid__toolbar {
    display: none;
  }
}

/* Dark Mode */
@media (prefers-color-scheme: dark) {
  .grid__toolbar {
    background: var(--theme-bg-secondary, #404040);
    border-bottom-color: var(--theme-border-color, #404040);
  }
  
  .grid__search-input {
    background: var(--theme-input-background, #2d2d2d);
    border-color: var(--theme-border-color, #404040);
    color: var(--theme-text-color, #ffffff);
  }
  
  .grid__export-select {
    background: var(--theme-input-background, #2d2d2d);
    border-color: var(--theme-border-color, #404040);
    color: var(--theme-text-color, #ffffff);
  }
}
