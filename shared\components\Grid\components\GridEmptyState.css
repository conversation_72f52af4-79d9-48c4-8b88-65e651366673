/* Grid Empty State */
.grid__empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: var(--theme-spacing-xl, 32px);
  background: var(--theme-bg-primary, #ffffff);
}

.grid__empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 400px;
  gap: var(--theme-spacing-md, 16px);
}

.grid__empty-icon {
  font-size: 48px;
  color: var(--theme-text-muted, #8a8886);
  opacity: 0.7;
}

.grid__empty-title {
  margin: 0;
  font-size: var(--theme-font-size-lg, 18px);
  font-weight: 600;
  color: var(--theme-text-primary, #323130);
}

.grid__empty-description {
  margin: 0;
  font-size: var(--theme-font-size-md, 14px);
  color: var(--theme-text-secondary, #605e5c);
  line-height: 1.5;
}

.grid__empty-action {
  margin-top: var(--theme-spacing-sm, 8px);
}

/* Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.grid__empty-content {
  animation: fadeInUp 0.3s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
  .grid__empty-state {
    min-height: 200px;
    padding: var(--theme-spacing-lg, 24px);
  }
  
  .grid__empty-icon {
    font-size: 36px;
  }
  
  .grid__empty-title {
    font-size: var(--theme-font-size-md, 16px);
  }
  
  .grid__empty-description {
    font-size: var(--theme-font-size-sm, 12px);
  }
}

@media (max-width: 480px) {
  .grid__empty-state {
    min-height: 150px;
    padding: var(--theme-spacing-md, 16px);
  }
  
  .grid__empty-content {
    gap: var(--theme-spacing-sm, 8px);
  }
  
  .grid__empty-icon {
    font-size: 28px;
  }
  
  .grid__empty-title {
    font-size: var(--theme-font-size-sm, 14px);
  }
  
  .grid__empty-description {
    font-size: var(--theme-font-size-xs, 12px);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .grid__empty-title {
    color: var(--theme-text-primary, #000000);
  }
  
  .grid__empty-description {
    color: var(--theme-text-secondary, #333333);
  }
  
  .grid__empty-icon {
    opacity: 1;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .grid__empty-content {
    animation: none;
  }
}

/* Print Styles */
@media print {
  .grid__empty-state {
    min-height: auto;
    padding: var(--theme-spacing-md, 16px);
  }
  
  .grid__empty-action {
    display: none;
  }
}

/* Dark Mode */
@media (prefers-color-scheme: dark) {
  .grid__empty-state {
    background: var(--theme-bg-primary, #2d2d2d);
  }
  
  .grid__empty-title {
    color: var(--theme-text-primary, #ffffff);
  }
  
  .grid__empty-description {
    color: var(--theme-text-secondary, #cccccc);
  }
  
  .grid__empty-icon {
    color: var(--theme-text-muted, #8a8886);
  }
}
