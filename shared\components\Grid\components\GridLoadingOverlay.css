/* Grid Loading Overlay */
.grid__loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.grid__loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--theme-spacing-md, 16px);
  padding: var(--theme-spacing-lg, 24px);
  background: var(--theme-bg-primary, #ffffff);
  border-radius: var(--theme-border-radius, 8px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--grid-border-color, var(--theme-border-color, #e1e5e9));
}

.grid__loading-message {
  color: var(--theme-text-secondary, #605e5c);
  font-size: var(--theme-font-size-md, 14px);
  font-weight: 500;
  text-align: center;
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.grid__loading-overlay {
  animation: fadeIn 0.2s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
  .grid__loading-content {
    padding: var(--theme-spacing-md, 16px);
    gap: var(--theme-spacing-sm, 8px);
  }
  
  .grid__loading-message {
    font-size: var(--theme-font-size-sm, 12px);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .grid__loading-overlay {
    background: rgba(255, 255, 255, 0.95);
  }
  
  .grid__loading-content {
    border-width: 2px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .grid__loading-overlay {
    animation: none;
  }
}

/* Dark Mode */
@media (prefers-color-scheme: dark) {
  .grid__loading-overlay {
    background: rgba(0, 0, 0, 0.8);
  }
  
  .grid__loading-content {
    background: var(--theme-bg-primary, #2d2d2d);
    border-color: var(--theme-border-color, #404040);
  }
  
  .grid__loading-message {
    color: var(--theme-text-secondary, #cccccc);
  }
}
