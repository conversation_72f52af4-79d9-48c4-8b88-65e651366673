/* Grid Cell */
.grid__cell {
  display: flex;
  align-items: center;
  padding: var(--grid-cell-padding, 8px 12px);
  border-right: 1px solid var(--grid-border-color, var(--theme-border-color, #e1e5e9));
  background: inherit;
  min-height: inherit;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.grid__cell:last-child {
  border-right: none;
}

.grid__cell:focus {
  outline: 2px solid var(--grid-primary-color, var(--theme-primary-color, #0078d4));
  outline-offset: -2px;
  z-index: 2;
}

/* Cell Content */
.grid__cell-content {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Cell Types */
.grid__cell--type-text .grid__cell-content {
  text-align: left;
}

.grid__cell--type-number .grid__cell-content {
  text-align: right;
  font-variant-numeric: tabular-nums;
}

.grid__cell--type-date .grid__cell-content {
  text-align: center;
  font-variant-numeric: tabular-nums;
}

.grid__cell--type-boolean .grid__cell-content {
  text-align: center;
  justify-content: center;
}

/* Value Renderers */
.grid__text-value {
  color: var(--theme-text-primary, #323130);
}

.grid__number-value {
  color: var(--theme-text-primary, #323130);
  font-weight: 500;
}

.grid__date-value {
  color: var(--theme-text-primary, #323130);
  font-size: var(--theme-font-size-sm, 14px);
}

.grid__boolean-value {
  font-size: 16px;
  font-weight: bold;
}

.grid__boolean-value:contains("✓") {
  color: var(--theme-success-color, #107c10);
}

.grid__boolean-value:contains("✗") {
  color: var(--theme-error-color, #d13438);
}

/* Editable Cell */
.grid__cell--editable {
  cursor: pointer;
}

.grid__cell--editable:hover {
  background-color: var(--theme-hover-background, #f3f2f1);
}

/* Editing State */
.grid__cell--editing {
  background-color: var(--theme-bg-primary, #ffffff);
  box-shadow: inset 0 0 0 2px var(--grid-primary-color, var(--theme-primary-color, #0078d4));
  z-index: 10;
}

.grid__cell--editing .grid__cell-content {
  width: 100%;
}

.grid__cell--editing input,
.grid__cell--editing select,
.grid__cell--editing textarea {
  width: 100%;
  border: none;
  outline: none;
  background: transparent;
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  padding: 0;
  margin: 0;
}

.grid__cell--editing input[type="checkbox"] {
  width: auto;
  transform: scale(1.2);
}

.grid__cell--editing select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=US-ASCII,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'><path fill='%23666' d='M2 0L0 2h4zm0 5L0 3h4z'/></svg>");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 8px;
  padding-right: 24px;
}

/* Pinned Columns */
.grid__cell--pinned-left {
  position: sticky;
  left: 0;
  z-index: 5;
  background: inherit;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

.grid__cell--pinned-right {
  position: sticky;
  right: 0;
  z-index: 5;
  background: inherit;
  box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
}

/* Validation States */
.grid__cell--error {
  background-color: var(--theme-error-background, #fef7f7);
  border-color: var(--theme-error-color, #d13438);
}

.grid__cell--warning {
  background-color: var(--theme-warning-background, #fffef7);
  border-color: var(--theme-warning-color, #f7630c);
}

.grid__cell--success {
  background-color: var(--theme-success-background, #f7fef7);
  border-color: var(--theme-success-color, #107c10);
}

/* Loading State */
.grid__cell--loading {
  position: relative;
  pointer-events: none;
}

.grid__cell--loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid var(--theme-border-color, #e1e5e9);
  border-top-color: var(--grid-primary-color, var(--theme-primary-color, #0078d4));
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .grid__cell {
    padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 6px);
    font-size: var(--theme-font-size-sm, 12px);
  }
  
  .grid__cell-content {
    font-size: var(--theme-font-size-sm, 12px);
  }
  
  .grid__boolean-value {
    font-size: 14px;
  }
  
  .grid__cell--editing input,
  .grid__cell--editing select {
    font-size: var(--theme-font-size-sm, 12px);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .grid__cell {
    border-right-width: 2px;
  }
  
  .grid__cell:focus {
    outline-width: 3px;
  }
  
  .grid__cell--editing {
    box-shadow: inset 0 0 0 3px var(--grid-primary-color, var(--theme-primary-color, #0078d4));
  }
  
  .grid__boolean-value {
    font-weight: 900;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .grid__cell--loading::after {
    animation: none;
  }
  
  .grid__cell--editable {
    transition: none;
  }
}

/* Print Styles */
@media print {
  .grid__cell {
    border-right: 1px solid #000;
    padding: 4px 8px;
  }
  
  .grid__cell--editing {
    box-shadow: none;
    border: 2px solid #000;
  }
  
  .grid__cell--loading::after {
    display: none;
  }
}

/* Touch Devices */
@media (hover: none) and (pointer: coarse) {
  .grid__cell {
    min-height: 44px; /* Minimum touch target */
    padding: var(--theme-spacing-sm, 8px) var(--theme-spacing-md, 12px);
  }
  
  .grid__cell--editable {
    min-height: 48px;
  }
  
  .grid__cell--editing input[type="checkbox"] {
    transform: scale(1.5);
  }
  
  .grid__cell--editing select {
    background-size: 12px;
    padding-right: 32px;
  }
}

/* Dark Mode */
@media (prefers-color-scheme: dark) {
  .grid__cell {
    border-right-color: var(--theme-border-color, #404040);
  }
  
  .grid__cell--editable:hover {
    background-color: var(--theme-hover-background, #333333);
  }
  
  .grid__cell--editing {
    background-color: var(--theme-bg-primary, #2d2d2d);
  }
  
  .grid__text-value,
  .grid__number-value,
  .grid__date-value {
    color: var(--theme-text-primary, #ffffff);
  }
  
  .grid__cell--error {
    background-color: rgba(209, 52, 56, 0.1);
  }
  
  .grid__cell--warning {
    background-color: rgba(247, 99, 12, 0.1);
  }
  
  .grid__cell--success {
    background-color: rgba(16, 124, 16, 0.1);
  }
}
