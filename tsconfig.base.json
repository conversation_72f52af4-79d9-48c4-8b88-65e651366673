{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "allowJs": false, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictBindCallApply": false, "strictPropertyInitialization": false, "noImplicitThis": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "noUncheckedIndexedAccess": false, "exactOptionalPropertyTypes": false, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "baseUrl": ".", "paths": {"@shared/*": ["./shared/*"], "@shared/components": ["./shared/components"], "@shared/components/*": ["./shared/components/*"], "@shared/services": ["./shared/services"], "@shared/services/*": ["./shared/services/*"], "@shared/utils": ["./shared/utils"], "@shared/utils/*": ["./shared/utils/*"], "@pcf/shared/*": ["./pcf/shared/*"]}}, "include": ["shared/**/*", "apps/**/*", "pcf/**/*"], "exclude": ["node_modules", "dist", "build"]}