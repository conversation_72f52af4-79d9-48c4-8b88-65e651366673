# API Documentation

This document provides comprehensive API documentation for the CRM React Apps monorepo components, services, and utilities.

## Table of Contents

- [Components](#components)
  - [Button](#button)
  - [LoadingSpinner](#loadingspinner)
  - [ThemeSwitcher](#themeswitcher)
- [Services](#services)
  - [Theme Service](#theme-service)
  - [API Client](#api-client)
  - [Authentication](#authentication)
- [Utilities](#utilities)
  - [Date Formatting](#date-formatting)
  - [Validation](#validation)
  - [Logger](#logger)

## Components

### Button

A theme-aware button component with multiple variants and states.

#### Props

```typescript
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
}
```

#### Usage

```tsx
import { Button } from '@shared/components';

// Basic usage
<Button onClick={handleClick}>Click me</Button>

// With variant and size
<Button variant="primary" size="large">
  Primary Button
</Button>

// Loading state
<Button loading={isLoading} disabled={isLoading}>
  {isLoading ? 'Saving...' : 'Save'}
</Button>

// Outline variant
<Button variant="outline" size="small">
  Cancel
</Button>
```

#### CSS Classes

- `.button` - Base button styles
- `.button-primary` - Primary variant styles
- `.button-secondary` - Secondary variant styles
- `.button-outline` - Outline variant styles
- `.button-small` - Small size styles
- `.button-medium` - Medium size styles (default)
- `.button-large` - Large size styles
- `.button-loading` - Loading state styles

### LoadingSpinner

A customizable loading spinner with theme support.

#### Props

```typescript
interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  message?: string;
  className?: string;
}
```

#### Usage

```tsx
import { LoadingSpinner } from '@shared/components';

// Basic usage
<LoadingSpinner />

// With message
<LoadingSpinner message="Loading data..." />

// Different sizes
<LoadingSpinner size="large" message="Processing..." />
```

#### CSS Classes

- `.loading-spinner-container` - Container wrapper
- `.loading-spinner` - Spinner element
- `.loading-spinner-circle` - Animated circle
- `.loading-spinner-small` - Small size modifier
- `.loading-spinner-large` - Large size modifier
- `.loading-spinner-message` - Message text

### ThemeSwitcher

A component for switching between different themes.

#### Props

```typescript
interface ThemeSwitcherProps {
  compact?: boolean;
  showLabels?: boolean;
  className?: string;
}
```

#### Usage

```tsx
import { ThemeSwitcher } from '@shared/components';

// Basic usage
<ThemeSwitcher />

// Compact mode
<ThemeSwitcher compact />

// With labels
<ThemeSwitcher showLabels />
```

## Services

### Theme Service

Provides theme management functionality with React Context API.

#### ThemeProvider

```typescript
interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: ThemeMode;
  enableAutoDetection?: boolean;
  enablePersistence?: boolean;
  storageKey?: string;
}
```

#### Hooks

##### useTheme

```typescript
const {
  currentTheme,     // Current active theme
  themeConfig,      // Theme configuration object
  isLoading,        // Loading state
  error,            // Error state
  switchTheme,      // Function to switch themes
  resetTheme,       // Function to reset to default
  applyTheme        // Function to apply specific theme
} = useTheme();
```

##### useThemeStyles

```typescript
const {
  currentTheme,     // Current active theme
  themeConfig,      // Theme configuration object
  getThemeClass,    // Get theme-aware CSS class
  getThemeStyle,    // Get theme-specific styles
  getCSSVariable    // Get CSS custom property value
} = useThemeStyles();
```

##### useCurrentTheme

```typescript
const currentTheme = useCurrentTheme(); // Returns current ThemeMode
```

##### useIsTheme

```typescript
const isCRMTheme = useIsTheme(ThemeMode.CRM); // Returns boolean
```

#### Usage

```tsx
import { ThemeProvider, useTheme, ThemeMode } from '@shared/services/theme';

// Wrap your app
function App() {
  return (
    <ThemeProvider enableAutoDetection enablePersistence>
      <MyApp />
    </ThemeProvider>
  );
}

// Use in components
function MyComponent() {
  const { currentTheme, switchTheme } = useTheme();
  const { getThemeClass, getCSSVariable } = useThemeStyles();
  
  return (
    <div className={getThemeClass('my-component')}>
      <p style={{ color: getCSSVariable('--theme-primary') }}>
        Current theme: {currentTheme}
      </p>
      <button onClick={() => switchTheme(ThemeMode.MFE)}>
        Switch to MFE Theme
      </button>
    </div>
  );
}
```

### API Client

Deployment-aware API client that works in both Dynamics 365 and standalone modes.

#### Usage

```typescript
import { getApiClient } from '@shared/services/api';

// Get API client (automatically detects deployment mode)
const apiClient = await getApiClient();

// Retrieve records
const contacts = await apiClient.retrieveMultipleRecords('contact', {
  select: ['fullname', 'emailaddress1'],
  filter: "statecode eq 0"
});

// Create record
const newContact = await apiClient.createRecord('contact', {
  firstname: 'John',
  lastname: 'Doe',
  emailaddress1: '<EMAIL>'
});

// Update record
await apiClient.updateRecord('contact', contactId, {
  emailaddress1: '<EMAIL>'
});

// Delete record
await apiClient.deleteRecord('contact', contactId);
```

### Authentication

Unified authentication service supporting both Dynamics 365 and MSAL.

#### Usage

```typescript
import { useAuth } from '@shared/services/auth';

function MyComponent() {
  const { user, isAuthenticated, login, logout, isLoading } = useAuth();
  
  if (isLoading) return <LoadingSpinner />;
  
  if (!isAuthenticated) {
    return <button onClick={login}>Login</button>;
  }
  
  return (
    <div>
      <p>Welcome, {user?.displayName}</p>
      <button onClick={logout}>Logout</button>
    </div>
  );
}
```

## Utilities

### Date Formatting

Comprehensive date formatting utilities using date-fns.

#### Functions

```typescript
// Format date with default format (MM/dd/yyyy)
formatDate(date: DateInput): string

// Format date with custom format
formatDate(date: DateInput, format: string): string

// Format date and time
formatDateTime(date: DateInput): string

// Format relative time (e.g., "2 hours ago")
formatRelativeTime(date: DateInput): string

// Format for API consumption
formatDateForApi(date: DateInput): string

// Parse date from various inputs
parseDate(input: DateInput): Date

// Validate date
isValidDate(date: any): boolean

// Get start/end of day
getStartOfDay(date: DateInput): Date
getEndOfDay(date: DateInput): Date
```

#### Usage

```typescript
import { formatDate, formatDateTime, formatRelativeTime } from '@shared/utils';

const now = new Date();
const formatted = formatDate(now); // "12/25/2023"
const withTime = formatDateTime(now); // "12/25/2023 10:30 AM"
const relative = formatRelativeTime(now); // "just now"
```

### Validation

Form validation utilities with comprehensive error handling.

#### Functions

```typescript
// Email validation
validateEmail(email: string): ValidationResult

// Password validation
validatePassword(password: string): ValidationResult

// Phone number validation
validatePhoneNumber(phone: string): ValidationResult

// Required field validation
validateRequired(value: any, fieldName: string): ValidationResult

// String length validation
validateLength(value: string, min: number, max: number, fieldName: string): ValidationResult

// Number range validation
validateNumberRange(value: number, min: number, max: number, fieldName: string): ValidationResult

// URL validation
validateUrl(url: string): ValidationResult

// Object validation with schema
validateObject<T>(obj: T, schema: ValidationSchema<T>): ValidationResult
```

#### Usage

```typescript
import { validateEmail, validateRequired, validateLength } from '@shared/utils';

// Single field validation
const emailResult = validateEmail('<EMAIL>');
if (!emailResult.isValid) {
  console.log(emailResult.errors); // Array of error messages
}

// Form validation
const formData = { email: '<EMAIL>', name: 'John' };
const schema = {
  email: validateEmail,
  name: (value: string) => validateRequired(value, 'Name')
};

const result = validateObject(formData, schema);
if (!result.isValid) {
  console.log(result.errors); // All validation errors
}
```

### Logger

Structured logging utility with different log levels.

#### Usage

```typescript
import { logger } from '@shared/utils';

// Different log levels
logger.info('Application started');
logger.warn('This is a warning');
logger.error('An error occurred', error);
logger.debug('Debug information'); // Only in development

// With structured data
logger.info('User action', { userId: '123', action: 'login' });
```

#### Configuration

```typescript
import { Logger, LogLevel } from '@shared/utils';

// Create custom logger
const customLogger = new Logger({
  level: LogLevel.INFO,
  enableConsole: true,
  enableRemote: false
});
```

## Type Definitions

### Theme Types

```typescript
enum ThemeMode {
  CRM = 'crm',
  MFE = 'mfe'
}

interface ThemeConfig {
  mode: ThemeMode;
  primaryColor: string;
  secondaryColor: string;
  fontFamily: string;
  // ... other theme properties
}
```

### Validation Types

```typescript
interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

type ValidationSchema<T> = {
  [K in keyof T]: (value: T[K]) => ValidationResult;
};
```

### Date Types

```typescript
type DateInput = Date | string | number | null | undefined;
```

## Error Handling

All services and utilities include comprehensive error handling:

```typescript
try {
  const result = await apiClient.retrieveMultipleRecords('contact');
  // Handle success
} catch (error) {
  logger.error('Failed to retrieve contacts', error);
  // Handle error
}
```

## Best Practices

1. **Always use TypeScript types** for better development experience
2. **Handle loading and error states** in components
3. **Use theme hooks** for consistent styling
4. **Validate user inputs** before processing
5. **Log errors appropriately** for debugging
6. **Test components** with different themes
7. **Follow accessibility guidelines** in component usage
